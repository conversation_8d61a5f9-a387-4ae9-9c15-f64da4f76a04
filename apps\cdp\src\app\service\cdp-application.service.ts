import { ApplicationService } from '@hcl/angular/unica-angular-common';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Injectable } from '@angular/core';
import { CdpUserService } from './cdp-user.service';
import { Router } from '@angular/router';
import { UnicaIconRegistryService } from '@hcl/angular/unica-icon';
import { cdpIconList } from '../constant/cdp-icons';

/**
 * The base application service for CDP
 */
@UntilDestroy()
@Injectable({
  providedIn: 'root'
})
export class CdpApplicationService extends ApplicationService  {
  /**
   * Default constructor
   */
  constructor(userService: CdpUserService,
              private router: Router,
              private iconService: UnicaIconRegistryService) {
    super(userService);
    this.userService.userDetailsUpdate$.pipe(untilDestroyed(this)).subscribe();
  }
  /**
   * Initialize all the details for the application to load here
   */
  public override async  init(): Promise<unknown> {
    // register custom CDP icons
    cdpIconList.forEach((icon) => {
      this.iconService.registerIcon(icon);
    });
    return new Promise((resolve, reject) => {
      this.userService.whoAmI().subscribe({
        next: (u) => {
          if (u) {
            // we have the user details, navigate to home
            this.router.navigate(['/dataplatform']);
          }
          // this.userDetailsSubject.next(u);
          // this.userDetails = u;
          resolve(true);
        },
        error: (error) => {
          resolve(true);
        }
      })
    });
  }
  /**
   * Get the authorization headers
   */
  public override getAuthorizationHeaders(): {[key: string]: string} | undefined {
    const cdpTokenDetails = this.userService.getTokenDetails();
    if (cdpTokenDetails) {
      return {
        Authorization: 'Bearer ' + cdpTokenDetails.accessToken
      }
    }
    return undefined;
  }
}
