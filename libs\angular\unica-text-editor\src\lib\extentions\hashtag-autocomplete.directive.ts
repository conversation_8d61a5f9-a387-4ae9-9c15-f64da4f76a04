import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  OnInit,
  ChangeDetectorRef,
  NgZone,
} from '@angular/core';
import Quill from 'quill';
import { AfterViewInit } from '@angular/core';
import { AutoSuggestNode } from './autosuggest-blot';
import { EditorTextNode, TextNodeClickEvent } from './text-node';
import { Subscription } from 'rxjs';
import {
  HashtagPopupService,
  SuggestionItem,
  PopupPosition,
  PopupCallbacks,
} from './hashtag-popup.service';

// Import Parchment for blot operations
const Parchment = Quill.import('parchment');

@Directive({
  selector: '[unicaHashtagAutocomplete]',
  standalone: true,
})
export class HashtagAutocompleteDirective
  implements OnInit, OnDestroy, AfterViewInit
{
  @Input() suggestions: SuggestionItem[] = [];
  @Input() quillEditor?: unknown; // Will be passed from the template - will be cast to <PERSON>uill later
  @Output() suggestionSelected = new EventEmitter<SuggestionItem>();

  private quillEditorRef: Quill | null = null;

  // State management
  private isAutoSuggestMode = false;

  // Hashtag tracking
  private charAfterSpecialSymbol = '';
  private hashtagStartPosition: number | null = null;
  private rangeToInsertHashtag: { index: number; length: number } | null = null;

  // Navigation
  private autoSuggestCharList: string[] = [];

  // Original suggestions list for filtering
  private originalSuggestions: SuggestionItem[] = [];

  // Last typed character tracking
  private lastTypedChar = '';

  // Subscription for hashtag click events
  private hashtagClickSubscription?: Subscription;

  // Edit mode tracking
  private isEditMode = false;
  private editingHashtagNode?: HTMLElement;

  // Popup callbacks
  private popupCallbacks: PopupCallbacks;

  constructor(
    private el: ElementRef,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private popupService: HashtagPopupService,
  ) {
    // Initialize popup callbacks
    this.popupCallbacks = {
      onItemSelected: (item: SuggestionItem) => this.handleItemSelected(item),
      onMenuClosed: () => this.handleMenuClosed(),
    };
  }

  ngOnInit(): void {
    // Initialize original suggestions
    this.originalSuggestions = [...this.suggestions];

    // Register autosuggest blot if not already registered
    try {
      Quill.register('formats/autosuggest', AutoSuggestNode, true);
    } catch (error) {
      // Blot might already be registered, ignore error
    }
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      // Initialize popup service
      this.popupService.initialize();

      // Use the quillEditor if provided by the parent component
      if (this.quillEditor) {
        this.quillEditorRef = this.quillEditor as unknown as Quill;
        this.setupQuillListeners();
      } else {
        // Otherwise try to find it in the DOM
        this.findQuillInstance();
      }

      // Subscribe to hashtag click events
      this.subscribeToHashtagClicks();
    }, 1000);
  }
  private setupQuillListeners(): void {
    if (this.quillEditorRef) {
      // Initialize editor event handlers
      this.initEditorEventHandlers();
    }
  }

  private findQuillInstance(): void {
    // Find the Quill instance in the DOM as a fallback
    setTimeout(() => {
      // Try different approaches to find the quill instance
      const quillContainer = this.el.nativeElement.closest('.ql-container');
      if (quillContainer) {
        const quillEditor = (quillContainer as { __quill?: unknown })
          .__quill as Quill | undefined;
        if (quillEditor) {
          this.quillEditorRef = quillEditor;
          this.setupQuillListeners();
        }
      }
    }, 0);
  }

  /**
   * Initialize editor event handlers for hashtag detection
   */
  private initEditorEventHandlers(): void {
    if (!this.quillEditorRef) return;

    this.quillEditorRef.root.addEventListener(
      'keydown',
      this.keyDownEventHandler.bind(this),
    );
    this.quillEditorRef.root.addEventListener(
      'click',
      this.mouseDownEventHandler.bind(this),
    );
    this.quillEditorRef.root.addEventListener(
      'paste',
      this.pasteEventHandler.bind(this),
    );
  }

  ngOnDestroy(): void {
    // Destroy popup service
    this.popupService.destroy();

    // Unsubscribe from hashtag click events
    if (this.hashtagClickSubscription) {
      this.hashtagClickSubscription.unsubscribe();
    }

    // Remove event listeners from Quill editor
    if (this.quillEditorRef) {
      this.quillEditorRef.root.removeEventListener(
        'keydown',
        this.keyDownEventHandler.bind(this),
      );
      this.quillEditorRef.root.removeEventListener(
        'click',
        this.mouseDownEventHandler.bind(this),
      );
      this.quillEditorRef.root.removeEventListener(
        'paste',
        this.pasteEventHandler.bind(this),
      );
    }
  }

  /**
   * Handle item selection from popup
   */
  private handleItemSelected(item: SuggestionItem): void {
    // Handle item selection

    if (this.isEditMode && this.editingHashtagNode) {
      // Edit mode: replace the existing hashtag
      this.replaceExistingHashtag(item.id, item.display);
    } else {
      // Normal mode: insert new hashtag (preserve isAutoSuggestMode for insertion)
      this.insertNewHashtag(item.id, item.display);
    }

    // Reset edit mode
    this.isEditMode = false;
    this.editingHashtagNode = undefined;

    // Clean up after successful insertion (now reset auto-suggest mode)
    this.postPopUpCloseCallback();

    // Emit selection event
    this.suggestionSelected.emit(item);
  }

  /**
   * Handle menu closed from popup
   */
  private handleMenuClosed(): void {
    // Only clean up if menu was closed without item selection
    // (item selection handles its own cleanup)
    if (this.isAutoSuggestMode) {
      this.postPopUpCloseCallback();
    }
  }

  /**
   * Subscribe to hashtag click events from the text node blot
   */
  private subscribeToHashtagClicks(): void {
    this.hashtagClickSubscription = EditorTextNode.TextNodeClick$.subscribe(
      (clickEvent: TextNodeClickEvent) => {
        if (clickEvent.action === 'edit' && clickEvent.node) {
          this.openDropdownForEdit(clickEvent);
        }
      },
    );
  }

  /**
   * Open dropdown for editing an existing hashtag
   */
  private openDropdownForEdit(clickEvent: TextNodeClickEvent): void {
    if (!this.quillEditorRef || !clickEvent.node) return;

    // Set edit mode
    this.isEditMode = true;
    this.editingHashtagNode = clickEvent.node;

    // Find the current hashtag in suggestions and pre-select it
    const currentHashtag = this.originalSuggestions.find(
      (s) => s.id === clickEvent.value.id,
    );

    if (currentHashtag) {
      // Set the current suggestions to show all options
      this.suggestions = [...this.originalSuggestions];

      // Get position from the clicked element
      const rect = clickEvent.node.getBoundingClientRect();
      const position: PopupPosition = {
        top: rect.top,
        left: rect.left,
        height: rect.height,
      };

      // Open the popup menu
      this.popupService.openMenu(
        this.suggestions,
        position,
        this.popupCallbacks,
      );

      // After menu is created, scroll to and highlight the current selection
      setTimeout(() => {
        this.popupService.scrollToAndSelectItem(currentHashtag);
      }, 50);
    }
  }

  // Note: Click outside handling is now managed by the popup service

  /**
   * Handle paste events to reset hashtag detection
   */
  private pasteEventHandler(evt: Event): void {
    setTimeout(() => {
      this.resetLastTypedChar();
    }, 200);
  }

  /**
   * Handle mouse down events to detect hashtag element clicks
   */
  private mouseDownEventHandler(event: MouseEvent): void {
    const clickedElement = event.target as HTMLElement;
    if (
      clickedElement.classList.contains('droppable') &&
      !clickedElement.classList.contains('link-droppable')
    ) {
      this.hashtagElementClicked(event);
    }
  }

  /**
   * Handle key down events for hashtag detection and navigation
   */
  private keyDownEventHandler(event: KeyboardEvent): void {
    const e = event;

    // Detect hashtag trigger
    if (
      ((e.shiftKey && e.key === '#') || e.key === '#') &&
      this.lastTypedChar !== '#'
    ) {
      this.insertAutoSuggestNode();
      return; // Exit early after triggering hashtag
    }

    // Only handle navigation keys if popup is open
    if (this.popupService && this.popupService.isOpen()) {
      switch (e.code) {
        case 'Escape':
          this.popupService.closeMenu();
          event.preventDefault();
          event.stopPropagation();
          return;
        case 'ArrowUp':
          this.popupService.navigatePrevious();
          event.preventDefault();
          event.stopPropagation();
          return;
        case 'ArrowDown':
          this.popupService.navigateNext();
          event.preventDefault();
          event.stopPropagation();
          return;
        case 'Enter':
          // Prevent default behavior first
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();

          // Then select the item
          this.popupService.selectCurrentItem();
          return;
      }
    } else {
      // Handle other keys when popup is not open
      switch (e.code) {
        case 'Delete':
        case 'Backspace':
          this.deleteHashtagFields();
          break;
        case 'Space':
        case 'Tab':
          this.resetLastTypedChar();
          break;
      }
    }
  }

  /**
   * Handle hashtag element clicks
   */
  private hashtagElementClicked(event: MouseEvent): void {
    if (!this.quillEditorRef) return;

    const selectedRange = this.quillEditorRef.getSelection();
    const target = event.target as HTMLElement;

    // Handle Safari specific behavior
    const isSafari =
      /Safari/.test(navigator.userAgent) &&
      /Apple Computer/.test(navigator.vendor);
    if (isSafari) {
      target.classList.add('read-write');
    }

    try {
      const blot = (Parchment as any).find(target);
      if (blot) {
        const index = this.quillEditorRef.getIndex(blot);
        this.quillEditorRef.setSelection(
          index,
          blot.domNode.innerText.length,
          Quill.sources.SILENT,
        );

        this.rangeToInsertHashtag = this.getRange();
        this.openMenu(event, false, blot.domNode);
      }
    } catch (error) {
      console.warn('Error finding blot:', error);
    }
  }

  /**
   * Get current range from Quill editor
   */
  private getRange(): { index: number; length: number } {
    if (!this.quillEditorRef) {
      return { index: 0, length: 0 };
    }

    let range = this.quillEditorRef.getSelection();
    if (!range) {
      range = {
        index: this.quillEditorRef.getLength() - 1,
        length: 0,
      };
    }
    return range;
  }

  /**
   * Reset last typed character
   */
  private resetLastTypedChar(): void {
    this.lastTypedChar = '';
  }

  /**
   * Filter suggestions based on query
   */
  private filterSuggestions(query: string): SuggestionItem[] {
    if (!query) return this.originalSuggestions;

    const lowerQuery = query.toLowerCase();
    return this.originalSuggestions.filter((s) =>
      s.display.toLowerCase().includes(lowerQuery),
    );
  }

  // Note: Popup creation and removal is now handled by HashtagPopupService

  /**
   * Insert auto-suggest node when # is typed
   */
  private insertAutoSuggestNode(): void {
    if (!this.quillEditorRef) return;

    // Get the current cursor position AFTER the # was typed
    let range = this.getRange();
    const rangeIndex = range.index;
    const rangeLength = range.length;

    // If selected text, delete text and calc new range post deletion
    if (rangeLength) {
      this.deleteText(rangeIndex, rangeLength);
      range = this.getRange();
    }

    // Store the position where # was typed
    // The # character is at position (range.index - 1) because cursor moved after typing #
    this.rangeToInsertHashtag = {
      index: range.index - 1, // Position of the # character
      length: 1, // Include the # character in the length
    };

    // Auto-suggest node inserted

    // Reset the character tracking for new input
    this.charAfterSpecialSymbol = '';
    // this.autoSuggestCharList = ['#'];

    // Instead of creating an autosuggest blot, just open the menu at current position
    this.openMenuAtCurrentPosition();
  }

  /**
   * Open menu at current cursor position without creating autosuggest blot
   */
  private openMenuAtCurrentPosition(): void {
    if (!this.quillEditorRef) return;

    const selection = this.quillEditorRef.getSelection();
    if (selection) {
      const bounds = this.quillEditorRef.getBounds(selection.index);
      const editorContainer = this.quillEditorRef.container;

      if (bounds && editorContainer) {
        const rect = editorContainer.getBoundingClientRect();

        // Configure auto-suggest mode
        this.isAutoSuggestMode = true;
        this.resetLastTypedChar();
        this.charAfterSpecialSymbol = '';
        // this.autoSuggestCharList.push('#');
        this.initAutoSuggestModeEventHandlers();

        // Create position for popup
        const position: PopupPosition = {
          top: rect.top + bounds.top,
          left: rect.left + bounds.left,
          height: bounds.height,
        };

        // Open popup menu
        this.popupService.openMenu(
          this.suggestions,
          position,
          this.popupCallbacks,
        );
      }
    }
  }

  /**
   * Delete text from editor
   */
  private deleteText(from: number, length: number): void {
    if (this.quillEditorRef) {
      this.quillEditorRef.deleteText(from, length, Quill.sources.SILENT);
    }
  }
  /**
   * Open the hashtag suggestions menu (legacy method - now uses popup service)
   */
  private openMenu(
    event: any,
    autoSuggestMode: boolean,
    caretNodeDom?: HTMLElement,
  ): void {
    if (autoSuggestMode) {
      this.isAutoSuggestMode = true;
      this.resetLastTypedChar();
      this.charAfterSpecialSymbol = '';
      // this.autoSuggestCharList.push('#');
      this.initAutoSuggestModeEventHandlers();
    }

    if (caretNodeDom) {
      const rect = caretNodeDom.getBoundingClientRect();
      const position: PopupPosition = {
        top: rect.top,
        left: rect.left,
        height: rect.height,
      };

      this.popupService.openMenu(
        this.suggestions,
        position,
        this.popupCallbacks,
      );
    }
  }

  // Note: Menu closing is now handled by HashtagPopupService

  /**
   * Initialize auto-suggest mode event handlers
   */
  private initAutoSuggestModeEventHandlers(): void {
    if (this.quillEditorRef) {
      this.quillEditorRef.root.addEventListener(
        'keydown',
        this.getTypedChar,
        false,
      );
    }
  }

  // Note: Menu creation is now handled by HashtagPopupService

  // Note: Menu positioning and navigation is now handled by HashtagPopupService

  // Note: Mouse handling is now managed by HashtagPopupService

  private selectHashtagFromTarget(target: HTMLElement): void {
    const data = target.getAttribute('data-data');
    const label = target.getAttribute('data-label');

    if (!data || !label) {
      return;
    }

    // Check if we have a valid Quill editor reference
    if (!this.quillEditorRef) {
      return;
    }

    // Close the popup first to avoid interference
    this.postPopUpCloseCallback();

    if (this.isEditMode && this.editingHashtagNode) {
      // Edit mode: replace the existing hashtag
      this.replaceExistingHashtag(data, label);
    } else {
      // Normal mode: insert new hashtag
      this.insertNewHashtag(data, label);
    }

    // Reset edit mode
    this.isEditMode = false;
    this.editingHashtagNode = undefined;

    // Emit selection event
    this.suggestionSelected.emit({ id: data, display: label });
  }

  /**
   * Replace an existing hashtag with a new one
   */
  private replaceExistingHashtag(newId: string, newLabel: string): void {
    if (!this.quillEditorRef || !this.editingHashtagNode) return;

    try {
      // Use Quill's scroll to find the blot - this is the correct Quill 2.0 approach
      const scroll = this.quillEditorRef.scroll;
      const blot = scroll.find(this.editingHashtagNode);

      if (blot) {
        const index = this.quillEditorRef.getIndex(blot);
        const length = 1; // EmbedBlots have length 1

        // Delete the existing hashtag blot
        this.quillEditorRef.deleteText(index, length, 'user');

        // Insert the new hashtag at the same position
        const delta = [
          { retain: index },
          {
            insert: {
              editortextblock: {
                label: `<--${newLabel}-->`,
                id: newId,
                length: newLabel.length,
              },
            },
          },
        ];

        this.quillEditorRef.updateContents(delta, 'user');

        // Set cursor position after the new hashtag
        this.quillEditorRef.setSelection(index + 1, 0);
      } else {
        // Fallback: try to find the blot by searching for editortextblock format
        this.replaceHashtagFallback(newId, newLabel);
      }
    } catch (error) {
      // Fallback method
      this.replaceHashtagFallback(newId, newLabel);
    }
  }

  /**
   * Fallback method to replace hashtag by scanning the document
   */
  private replaceHashtagFallback(newId: string, newLabel: string): void {
    if (!this.quillEditorRef || !this.editingHashtagNode) return;

    try {
      // Search through the entire document for editortextblock formats
      const documentLength = this.quillEditorRef.getLength();

      for (let i = 0; i < documentLength; i++) {
        const format = this.quillEditorRef.getFormat(i, 1);
        if (format && format['editortextblock']) {
          // Check if this might be our hashtag by comparing the ID
          const hashtagData = format['editortextblock'] as any;
          if (hashtagData && hashtagData.id) {
            // For now, let's replace the first one we find (we can make this smarter later)

            // Delete the existing hashtag
            this.quillEditorRef.deleteText(i, 1, 'user');

            // Insert the new hashtag
            const delta = [
              { retain: i },
              {
                insert: {
                  editortextblock: {
                    label: `<--${newLabel}-->`,
                    id: newId,
                    length: newLabel.length,
                  },
                },
              },
            ];

            this.quillEditorRef.updateContents(delta, 'user');
            this.quillEditorRef.setSelection(i + 1, 0);

            return;
          }
        }
      }
    } catch (error) {
      // Silently handle errors
    }
  }

  /**
   * Insert a new hashtag (normal mode)
   */
  private insertNewHashtag(id: string, label: string): void {
    // Get current cursor position for insertion
    let insertionRange = this.rangeToInsertHashtag;

    if (!insertionRange) {
      insertionRange = this.getRange();
    }

    // Calculate what needs to be deleted
    let deleteLength = 0;
    if (this.isAutoSuggestMode) {
      // Delete the # character + any typed characters
      // Use the range length (which includes #) + any additional typed characters
      deleteLength = insertionRange.length + this.charAfterSpecialSymbol.length;
    }

    // Perform the insertion with proper range handling
    this.insertHashtagDirectly(id, label, insertionRange, deleteLength);
  }

  /**
   * Insert hashtag directly with proper range handling
   */
  private insertHashtagDirectly(
    id: string,
    label: string,
    range: { index: number; length: number },
    deleteLength: number,
  ): void {
    if (!this.quillEditorRef) {
      return;
    }
    const newLabel = `<--${label}-->`;

    try {
      console.log('Before operation:', {
        rangeIndex: range.index,
        deleteLength: deleteLength,
        editorContent: this.quillEditorRef.getText()
      });

      // Use a single atomic operation to delete and insert
      const delta: any[] = [];

      // Retain content before the deletion point
      if (range.index > 0) {
        delta.push({ retain: range.index });
      }

      // Delete the # character and any typed characters
      if (deleteLength > 0) {
        delta.push({ delete: deleteLength });
      }

      // Insert the hashtag blot
      delta.push({
        insert: {
          editortextblock: {
            label: newLabel,
            id: id,
            length: newLabel.length,
          },
        },
      });
      delta.push({ insert: ' ' });

      console.log('Applying atomic delta:', delta);

      // Apply the delta as a single atomic operation
      this.quillEditorRef.updateContents(delta, Quill.sources.USER);

      // Set cursor position after the hashtag
      const newCursorPosition = range.index + 2;
      this.quillEditorRef.setSelection(newCursorPosition, 0, Quill.sources.SILENT);

    } catch (error) {
      console.error('Error in hashtag insertion:', error);
    }
  }
  /**
   * Get typed character for auto-suggest mode
   */
  private getTypedChar = (event: KeyboardEvent): void => {
    const inputChar = String.fromCharCode(event.keyCode).toLowerCase();

    // Check for underscore key
    if (event.shiftKey && event.key === '_') {
      this.autoSuggestCharList.push(this.charAfterSpecialSymbol);
      this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + '_';
    }

    // Check for input was a letter, number, hyphen, underscore or space
    if (/[a-z0-9-_ ]/.test(inputChar) || event.keyCode === 8) {
      if (/[a-z0-9-_ ]/.test(inputChar)) {
        // all but delete key press
        this.autoSuggestCharList.push(this.charAfterSpecialSymbol);
        if (event.shiftKey && event.key === '#') {
          this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + event.key;
        } else {
          this.charAfterSpecialSymbol =
            this.charAfterSpecialSymbol +
            String.fromCharCode(event.keyCode).toLowerCase();
        }
      } else {
        // delete key press
        this.autoSuggestCharList.pop();
        this.charAfterSpecialSymbol = this.charAfterSpecialSymbol.slice(0, -1);
      }

      // Filter suggestions based on typed characters
      this.suggestions = this.originalSuggestions.filter((listItem) => {
        return (
          listItem.display
            .toLowerCase()
            .indexOf(this.charAfterSpecialSymbol.toLowerCase()) >= 0
        );
      });

      this.lastTypedChar = this.charAfterSpecialSymbol;

      // Update popup menu with filtered suggestions
      if (this.popupService.isOpen()) {
        if (this.suggestions && this.suggestions.length > 0) {
          // Get current cursor position for popup positioning
          const selection = this.quillEditorRef?.getSelection();
          if (selection && this.quillEditorRef) {
            const bounds = this.quillEditorRef.getBounds(selection.index);
            const editorContainer = this.quillEditorRef.container;

            if (bounds && editorContainer) {
              const rect = editorContainer.getBoundingClientRect();

              const position: PopupPosition = {
                top: rect.top + bounds.top,
                left: rect.left + bounds.left,
                height: bounds.height,
              };

              this.popupService.openMenu(
                this.suggestions,
                position,
                this.popupCallbacks,
              );
            }
          }
        } else {
          this.popupService.closeMenu();
        }
      }

      // Check if user deletes # when no char is there
      if (
        this.charAfterSpecialSymbol === '' &&
        !this.autoSuggestCharList.length
      ) {
        this.popupService.closeMenu();
      }
    }
  };

  /**
   * Delete hashtag fields when backspace is pressed
   */
  private deleteHashtagFields(): void {
    this.resetLastTypedChar();

    if (!this.quillEditorRef) return;

    const selectedRange = this.quillEditorRef.getSelection();

    if (selectedRange) {
      // Check if we're at a hashtag position
      const format = this.quillEditorRef.getFormat(selectedRange.index);

      // Only handle deletion if we're actually at a hashtag
      if (format && format['editortextblock']) {
        try {
          const blot = this.quillEditorRef.getLeaf(selectedRange.index);
          if (blot[0] && blot[0].domNode && blot[0].domNode.parentElement) {
            blot[0].domNode.parentElement.classList.add('read-write');
            const foundBlot = (Parchment as any).find(blot[0].domNode);
            if (foundBlot && foundBlot.domNode) {
              const textLength = foundBlot.domNode.innerText?.length || 0;
              this.quillEditorRef.setSelection(
                selectedRange.index - textLength,
                textLength,
                Quill.sources.SILENT,
              );
            }
          }
        } catch (error) {
          // Silently handle errors
        }
      }
    }
  }

  /**
   * Callback to run when popup is closed
   */
  private postPopUpCloseCallback(): void {
    this.charAfterSpecialSymbol = '';
    this.suggestions = [...this.originalSuggestions];
    this.isAutoSuggestMode = false;
    this.autoSuggestCharList = [];

    // Remove autosuggest elements
    const autoSuggestElements = document.querySelectorAll('.autosuggest');
    for (let i = 0; i < autoSuggestElements.length; i++) {
      const spanText = (autoSuggestElements[i] as HTMLElement).innerText;
      const parentNode = autoSuggestElements[i].parentNode;

      // Autosuggest node at line start, has some text content
      if ((autoSuggestElements[i] as HTMLElement).innerText.trim()) {
        const span = document.createElement('span');
        span.innerText = spanText.trim();
        span.style.backgroundColor = 'white';
        parentNode?.insertBefore(span, autoSuggestElements[i]);
      }
      parentNode?.removeChild(autoSuggestElements[i]);
    }

    // Remove event listeners
    if (this.quillEditorRef) {
      this.quillEditorRef.root.removeEventListener(
        'keydown',
        this.getTypedChar,
        false,
      );
    }
  }
}
