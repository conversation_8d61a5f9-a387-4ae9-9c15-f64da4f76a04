import { Injectable } from '@angular/core';
import { CdpUserPermissionService } from './cdp-user-permission.service';
import { map } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { CdpSideNavSectionGenerator } from '../constant/cdp-side-nav-constants';
import { UnicaSideNavElementItemConfig } from '@hcl/unica-common';

@Injectable()
export class CdpNavigationService {
  /**
   * Teh default side-nav has home & fav section, so this will create
   * 2 sections that will be rendered in the nav
   */
  public sideNav$ = this.userPermissionService.featureConfig$.pipe(
    map((c) => {
      const navList = CdpSideNavSectionGenerator((label: string) => this.translate.instant(label));
      // we have to filter this list as per user permissions
      return navList.filter((sec) => {
        // iterate through the elements in the section
        sec.elements = this.filterSideNavElementsAsPerPermissions(sec.elements);
        return sec.elements.length > 0;
      })
    })
  )
  /**
   *
   * @param userPermissionService
   */
  constructor(private userPermissionService: CdpUserPermissionService,
              private translate: TranslateService) {
  }

  /**
   * Filter the elements
   * @private
   */
  private filterSideNavElementsAsPerPermissions(list: UnicaSideNavElementItemConfig[]){
    return list.filter((element) => {
      // chk if there are children we will have to filter them
      if (element.children && element.children.length > 0) {
        element.children = this.filterSideNavElementsAsPerPermissions(element.children);
      }
      // chk if user has access to this id
      return this.userPermissionService.hasPermission(element.id);
    });
  }
}
