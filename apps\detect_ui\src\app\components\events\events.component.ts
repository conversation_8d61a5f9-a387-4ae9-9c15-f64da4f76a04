
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CommonService } from '../../services/common-service';

@Component({
  selector: 'hcldetect-events',
  templateUrl: './events.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class EventsComponent {
  @Input() title = '';
  constructor(public translate: TranslateService, public commonService: CommonService) { }

}
