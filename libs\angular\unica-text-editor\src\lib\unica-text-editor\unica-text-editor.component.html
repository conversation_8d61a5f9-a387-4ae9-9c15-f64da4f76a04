<div class="unica-text-editor-container">
  <quill-editor
    theme="bubble"
    [formats]="getQuillFormats()"
    [modules]="getQuillConfig()"
    [readOnly]="readOnly()"
    [ngModel]="html"
    [placeholder]="placeholder()"
    [required]="required()"
    [ngStyle]="getComputedStyles()"
    (onEditorCreated)="onEditorCreated($event)"
    (onContentChanged)="onContentChanged($event)"
    unicaTextAutocomplete
    [quillEditor]="editorInstance"
    [suggestions]="suggestionList"
  >
  </quill-editor>
  <!-- <div
    *ngIf="editorInstance"
    unicaHashtagAutocomplete
    [suggestions]="getDefaultHashtagSuggestions()"
    [quillEditor]="editorInstance"
    (suggestionSelected)="onHashtagSelected($event)"
    style="
      display: none;
      position: absolute;
      width: 0;
      height: 0;
      overflow: hidden;
    "
  ></div> -->
</div>
