import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';
import { CdpDashboard } from '../../model/cdp-dashboard';

@Injectable({
  providedIn: 'root'
})
export class CdpDashboardApiService {
  /**
   * Default constructor
   */
  constructor(private http: HttpClient) {
  }
  /**
   * Get the dashboard definition
   */
  public getDashboardDef(campId: number, id: number) {
    return this.http.get<{data: CdpDashboard}>(`/coreapi/-/v1/advertisers/${campId}/dashboard/${id}`)
      .pipe( map((d) => {
        if (typeof d.data.layout === 'string') {
          d.data.layout = JSON.parse(d.data.layout as string);
        }
        return d.data;
      }))
  }
}
