/**
 * Unica Text Editor Library
 *
 * A rich text editor component built on Quill.js with all styles bundled.
 * No additional style imports required by consumers.
 *
 * Usage:
 * ```typescript
 * import { UnicaTextEditorComponent } from '@hcl/angular/unica-text-editor';
 * ```
 *
 * ```html
 * <unica-text-editor
 *   [(content)]="myContent"
 *   [placeholder]="'Enter your text...'"
 *   [readOnly]="false">
 * </unica-text-editor>
 * ```
 */

export * from './lib/unica-text-editor/unica-text-editor.component';
export type { TextEditorStyles } from './lib/editor.model';
export * from './lib/utils/style.utils';
export { TextAutocompleteDirective } from './lib/directives';
export { QuillTagsService } from './lib/services';
export { QuillTagsHelper } from './lib/helpers';
export * from './lib/extentions'; // Legacy exports
