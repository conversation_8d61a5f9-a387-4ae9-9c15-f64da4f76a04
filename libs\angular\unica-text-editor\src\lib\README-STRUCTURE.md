# Unica Text Editor - Organized Structure

This document explains the new organized folder structure for better separation of concerns and maintainability.

## 📁 Folder Structure

```
libs/angular/unica-text-editor/src/lib/
├── directives/           # Angular directives
│   ├── text-autocomplete.directive.ts
│   └── index.ts
├── services/            # Angular services
│   ├── quill-tags.service.ts
│   └── index.ts
├── helpers/             # Helper utilities (optional)
│   ├── quill-tags.helper.ts
│   └── index.ts
├── extensions/          # Quill-specific extensions
│   ├── text-align.config.ts
│   ├── text-node.ts
│   └── index.ts
├── styles/              # SCSS style files
│   ├── quill-tags.styles.scss
│   ├── hashtag-autocomplete.styles.scss
│   └── index.ts
├── extentions/          # Legacy folder (for backward compatibility)
│   └── index.ts         # Re-exports from new structure
└── unica-text-editor/   # Main component
    ├── unica-text-editor.component.ts
    ├── unica-text-editor.component.html
    └── unica-text-editor.component.scss
```

## 🎯 Separation of Concerns

### **📋 Directives (`/directives`)**
- **Purpose**: Angular directives for DOM manipulation and user interaction
- **Contents**: 
  - `TextAutocompleteDirective` - Handles hashtag autocomplete functionality
- **Usage**: Import and use in Angular components

### **⚙️ Services (`/services`)**
- **Purpose**: Business logic and state management
- **Contents**:
  - `QuillTagsService` - Core Tags functionality for Quill editor
- **Usage**: Inject into components via Angular DI

### **🔧 Helpers (`/helpers`)**
- **Purpose**: Optional utility functions for easier integration
- **Contents**:
  - `QuillTagsHelper` - Wrapper for easier Tags service usage
- **Usage**: Use when you want simplified API (optional)

### **🔌 Extensions (`/extensions`)**
- **Purpose**: Quill-specific extensions and configurations
- **Contents**:
  - `TextAlignQuillModule` - Text alignment configuration
  - `EditorTextNode` - Custom Quill blot for text blocks
- **Usage**: Register with Quill before editor initialization

### **🎨 Styles (`/styles`)**
- **Purpose**: SCSS stylesheets for components
- **Contents**:
  - `quill-tags.styles.scss` - Tags picker styling
  - `hashtag-autocomplete.styles.scss` - Autocomplete popup styling
- **Usage**: Import in component SCSS files

## 📦 Import Patterns

### **Simple Direct Imports**
```typescript
// Recommended: Direct service usage
import { QuillTagsService } from '@hcl/angular/unica-text-editor/services';

// Direct directive usage
import { TextAutocompleteDirective } from '@hcl/angular/unica-text-editor/directives';

// Direct extension usage
import TextAlignQuillModule from '@hcl/angular/unica-text-editor/extensions/text-align.config';
```

### **Grouped Imports**
```typescript
// Import from category index
import { QuillTagsService, TagSuggestion } from '@hcl/angular/unica-text-editor/services';
import { TextAutocompleteDirective } from '@hcl/angular/unica-text-editor/directives';
import { QuillTagsHelper } from '@hcl/angular/unica-text-editor/helpers';
```

### **Legacy Imports (Backward Compatibility)**
```typescript
// Still works - imports from legacy extentions folder
import { QuillTagsService, TextAutocompleteDirective } from '@hcl/angular/unica-text-editor/extentions';
```

## 🎨 Style Imports

### **In Component SCSS**
```scss
// Import specific styles
@import '@hcl/angular/unica-text-editor/styles/quill-tags.styles.scss';
@import '@hcl/angular/unica-text-editor/styles/hashtag-autocomplete.styles.scss';
```

### **In Global Styles**
```scss
// Import all text editor styles
@import '@hcl/angular/unica-text-editor/styles/quill-tags.styles.scss';
@import '@hcl/angular/unica-text-editor/styles/hashtag-autocomplete.styles.scss';
```

## 🚀 Usage Examples

### **Simple Service Usage (Recommended)**
```typescript
import { Component, inject } from '@angular/core';
import { QuillTagsService, TagSuggestion } from '@hcl/angular/unica-text-editor/services';

@Component({
  selector: 'my-editor',
  template: `
    <unica-text-editor
      [suggestionList]="suggestions"
      (hashtagSelected)="onTagSelected($event)"
    ></unica-text-editor>
  `
})
export class MyEditorComponent {
  private tagsService = inject(QuillTagsService);
  
  suggestions: TagSuggestion[] = [
    { id: 'user-name', display: 'User Name' },
    { id: 'user-email', display: 'User Email' }
  ];

  onTagSelected(tag: TagSuggestion) {
    console.log('Tag selected:', tag);
  }
}
```

### **With Helper (Optional)**
```typescript
import { QuillTagsHelper } from '@hcl/angular/unica-text-editor/helpers';
import { QuillTagsService } from '@hcl/angular/unica-text-editor/services';

export class MyEditorComponent {
  private tagsService = inject(QuillTagsService);
  private helper = new QuillTagsHelper(this.tagsService);

  ngOnInit() {
    const tags = this.helper.setupWithDefaults((tag) => {
      console.log('Tag selected:', tag);
    });
    // Use tags.addToToolbar(), tags.getHandlers(), etc.
  }
}
```

### **Direct Directive Usage**
```typescript
import { TextAutocompleteDirective } from '@hcl/angular/unica-text-editor/directives';

@Component({
  imports: [TextAutocompleteDirective],
  template: `
    <div 
      textAutocomplete
      [suggestions]="suggestions"
      [triggerCharacters]="['#', '@']"
      (suggestionSelected)="onSuggestionSelected($event)"
    >
      Type # or @ to trigger autocomplete
    </div>
  `
})
export class MyComponent {
  suggestions = [
    { id: 'tag1', display: 'Tag 1' },
    { id: 'tag2', display: 'Tag 2' }
  ];
}
```

## 🔄 Migration Guide

### **From Old Structure**
```typescript
// Old way
import { QuillTagsService } from '@hcl/angular/unica-text-editor/extentions/quill-tags.service';

// New way (recommended)
import { QuillTagsService } from '@hcl/angular/unica-text-editor/services';

// Or still works (legacy)
import { QuillTagsService } from '@hcl/angular/unica-text-editor/extentions';
```

### **Style Migration**
```scss
// Old way
@import '../extentions/quill-tags.styles.scss';

// New way
@import '../styles/quill-tags.styles.scss';
@import '../styles/hashtag-autocomplete.styles.scss';
```

## 🎯 Benefits

### **✅ Better Organization**
- Clear separation of concerns
- Easy to find specific functionality
- Logical grouping of related files

### **✅ Improved Maintainability**
- Easier to update specific features
- Reduced coupling between components
- Clear dependency relationships

### **✅ Enhanced Developer Experience**
- Intuitive import paths
- Better IDE autocomplete
- Clearer code structure

### **✅ Scalability**
- Easy to add new directives/services
- Modular architecture
- Plugin-like extensibility

### **✅ Backward Compatibility**
- Legacy imports still work
- Gradual migration possible
- No breaking changes

## 📝 Best Practices

### **Import Preferences**
1. **Direct imports** from specific folders (recommended)
2. **Category imports** from index files
3. **Legacy imports** only for backward compatibility

### **File Organization**
- Keep related functionality together
- Use descriptive file names
- Maintain consistent naming conventions

### **Style Management**
- Import only needed styles
- Use specific style imports over global imports
- Maintain style isolation when possible

This new structure provides better organization while maintaining full backward compatibility!
