import Quill from 'quill';
import { Subject } from 'rxjs';

// Import EmbedBlot for standalone elements like hashtags
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const EmbedBlot = Quill.import('blots/embed') as any;

export interface TextNodeData {
  label: string;
  length: number;
  id?: string;
  messageBody?: string;
}

export interface TextNodeClickEvent {
  value: TextNodeData;
  action?: 'edit' | 'create';
  node?: HTMLElement;
}

export class EditorTextNode extends EmbedBlot {
  static blotName = 'editortextblock';
  static tagName = 'span';
  static className = 'droppable';

  static onTextNodeClick = new Subject<TextNodeClickEvent>();
  static TextNodeClick$ = EditorTextNode.onTextNodeClick.asObservable();

  static create(value: TextNodeData): HTMLElement {
    // Follow the exact pattern from Quill documentation
    const node = super.create() as HTMLElement;

    // Set content directly in the span
    node.innerHTML = value.label;

    // Set attributes - only id and contenteditable
    node.setAttribute('contenteditable', 'false');
    if (value.id) {
      node.id = value.id;
    }

    // Click event to open dropdown with current value
    node.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();

      // Emit click event with the current hashtag data
      EditorTextNode.onTextNodeClick.next({
        value,
        action: 'edit',
        node: node
      });
    });

    return node;
  }

  // Override value method to return the data stored in the node
  static value(node: HTMLElement): TextNodeData {
    return {
      label: node.innerHTML || '',
      length: (node.innerHTML || '').length,
      id: node.id || undefined
    };
  }
}

// Note: Blot is registered in the component to avoid conflicts
