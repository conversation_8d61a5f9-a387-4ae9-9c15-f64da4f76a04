
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { remoteRoutes } from './entry.routes';
import DashboardComponent from '../components/dashboard/dashboard.component';
import { TranslateModule } from '@ngx-translate/core';
import EventsComponent from '../components/events/events.component';

@NgModule({
  declarations: [DashboardComponent, EventsComponent],
  imports: [
    CommonModule,
    TranslateModule.forRoot(),
    RouterModule.forChild(remoteRoutes)
  ],
  providers: [],
  bootstrap: [DashboardComponent],
})
export class RemoteEntryModule { }
