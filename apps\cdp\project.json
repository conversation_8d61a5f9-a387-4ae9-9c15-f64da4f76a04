{"name": "cdp", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "cdp", "sourceRoot": "apps/cdp/src", "tags": ["app:cdp"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/cdp", "index": "apps/cdp/src/index.html", "main": "apps/cdp/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/cdp/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/cdp/public"}, {"glob": "**/*", "input": "public/assets", "output": "assets"}], "styles": ["apps/cdp/src/styles.scss", "public/assets/styles/unica/angular/unica.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/cdp/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/cdp/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4205, "proxyConfig": "proxy.conf.js", "publicHost": "http://localhost:4205"}, "configurations": {"production": {"buildTarget": "cdp:build:production"}, "development": {"buildTarget": "cdp:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "cdp:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/cdp/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "cdp:build", "port": 4205, "watch": false}, "configurations": {"development": {"buildTarget": "cdp:build:development"}, "production": {"buildTarget": "cdp:build:production"}}}}}