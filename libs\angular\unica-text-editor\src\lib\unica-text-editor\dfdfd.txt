import { TranslateService } from '@ngx-translate/core';
import { ChangeDetectorRef, Component, ElementRef, Input, QueryList, ViewChild, ViewChildren, ViewEncapsulation } from '@angular/core';
import { TextField } from '../../classes/Fields';
import { EditorEvents } from "../editor/editor.constant";
import { createPadding, createFont, createLineHeight, extractTextFromHTML, createWidthHeight } from '../../utils';
import { IpEmailBuilderService } from '../../ip-email-builder.service';
import { QuillEditorComponent, QuillToolbarConfig } from 'ngx-quill';
import Quill from 'quill';
import { Utility } from '../../../helper/Utility';
import { SubscriptionLike } from 'rxjs';
import { ModalService } from 'hcl-angular-widgets-lib';
const Parchment = Quill.import('parchment');
declare const _, window: any;

@Component({
  selector: 'ip-text-field',
  templateUrl: './text-field.component.html',
  styleUrls: ['./text-field.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class TextFieldComponent {

  /**
  * The populp menu that will open when user clicks on the personalized Fiels
  * TODO : this needs to be a auto-complete rather then a pop-up menu
  */

  @ViewChild("suggestionPopupMenu") suggestionPopupMenu: ElementRef;

  /**
  * Personalization List list value, used in text node click.
  */
  personalizationList;
  // keeps a copy of original Personalization List
  origpersonalizationList;

  caretNodeDom: HTMLElement;

  // Flag to indicate if a menu is opened.
  isMenuOpened: boolean = false;

  /**
   * Reference to text node clicked or updated
   */
  textNode;

  // Special character # index.
  rangeToInsertPersonalization: { index: number, length: number };

  // Flag to detect if a menu is opened, by typng hash, to distinguish from update menu op.
  isAutoSuggestMode: boolean;

  // last typed character
  lastTypedChar: string;

  // Characters tyoed after # Symbol, based on which the suggestion list for Personilazation list will come.
  charAfterSpecialSymbol: string;

  /**
   * Current item which is being referenced while doing key up / down
   * events in the PF List.
  **/

  traversedItemCount: number = -1;

  // current element which is being traversed.
  currentTraversedElement: HTMLElement;

  // List to maintain the list of chars pressed after #,
  // to track of things if user deleted #, pop up should close.
  autoSuggestCharList: string[] = [];

  // Flag for PF update action
  updatePersonalizationFieldAction: boolean = false;

  // store current range when an existing link is selected
  currentRangeForSelectedLink: any;
  // store current range when an existing text is selected for AI
  currentRangeForSelectedTextAI: any;

  private subscriptionList: Array<SubscriptionLike> = new Array<SubscriptionLike>();

  // @Input() field: TextField;
  _field: TextField;
  html: string
  maxAITextFieldActive: boolean;
  @Input()
  set field(b: TextField) {
    this._field = this.parseHTMLForHyperlinks(b);
    this.html = b.innerText;
    // make sure we get an update when there is some change in styling
    this._field.options.updateCallback = this.optionsUpdated.bind(this);
  }

  get field(): TextField {
    return this._field
  }

  /**
   * The instance of the quill editor
   */
  @ViewChild(QuillEditorComponent, { static: false }) quillEditorComponent: QuillEditorComponent;
  @ViewChild("suggestionList") suggestionList: ElementRef;
  constructor(public ngjs: IpEmailBuilderService,
    public translate: TranslateService,
    private _cdr: ChangeDetectorRef,
    private modalService: ModalService,
    private _ngb: IpEmailBuilderService) {
  }

  ngOnInit(): void {
    this.getTypedChar = this.getTypedChar.bind(this);
    this.closeMenu = this.closeMenu.bind(this);
    this.selectPersonalization = this.selectPersonalization.bind(this);
    this.mouseDownHandler = this.mouseDownHandler.bind(this);
    this.getpersonalizationList();
    this.subscriptionList.push(this.ngjs.updateRuleLengthForHyperlink$.subscribe(() => {
      this.setRuleLengthAttribute();
    }));
    this.subscriptionList.push(this.ngjs.showErrorForStaticLP$.subscribe(() => {
      this.html = this.field.innerText;
      this._cdr.detectChanges();
    }));
    this._ngb.maxAIActive$.subscribe(active => {
      this.maxAITextFieldActive = active;//temporary masking can be toggled here for testing purpose
    });
    this._ngb.currentEditingField$.subscribe((field) => {
      if (field && field.type === 'text-field' && this.field === field) {
        this.html = field.innerText;
      }
    });
  }

  parseHTMLForHyperlinks(b: TextField) {
    if (b.innerText) {
      const domObject = new DOMParser().parseFromString(b.innerText, 'text/html');
      const allLinks: any = domObject.querySelectorAll('a');
      allLinks.forEach((elem) => {
        if (!elem.getAttribute('data-id')) {
          elem.setAttribute('data-id', Utility.generateId());
        }
      });
      b.innerText = domObject.body.innerHTML;
    }
    return b;
  }

  optionsUpdated(x, y): void {
    switch (x) {
      case 'color':
        this.quillEditorComponent.quillEditor.formatText(0, this.quillEditorComponent.quillEditor.getLength() - 1, 'color', y.color);
        break;
    }
  }

  getParentStyles() {
    const { align } = this.field.options;

    return {
      'text-align': (align === 'center' ? 'center' : (align === 'left' ? 'left' : 'right')),
      width: '100%',
      display: 'block'
    };
  }

  getTextStyles() {
    const { color, font, lineHeight, padding, width } = this._field.options;

    return {
      color,
      width: (createWidthHeight(width) === 'auto' ? '100%' : createWidthHeight(width)),
      display: 'inline-block',
      ...createLineHeight(lineHeight),
      ...createFont(font),
      ...createPadding(padding)
    };
  }

  aiHandler(event) {
    // setTimeout(() => {
    //   this.maxAIPopupMenu.openMenu(event);
    // }, 0);
    // setTimeout(() => {
    //   const elements = document.getElementsByClassName(this?.maxAIRewritePopupMenuOptions?.class);
    //   if (elements.length > 0) {
    //       const buttonElement = document.querySelector('.ql-tooltip:not(.ql-hidden) span.ql-formats:last-child');
    //       const overlayElement = document.querySelector('.cdk-overlay-connected-position-bounding-box');
    //       if (buttonElement && overlayElement) {
    //           const checkElementVisibility = () => {
    //               const buttonRect = buttonElement.getBoundingClientRect();
    //               const overlayRect = overlayElement.getBoundingClientRect();
    //               if (buttonRect.width !== 0 && buttonRect.height !== 0 && overlayRect.width !== 0 && overlayRect.height !== 0) {
    //                   const leftOffset = buttonRect.left - overlayRect.left;
    //                   let menu = document.querySelector('.maxAI-menu')as HTMLElement;
    //                   for (let i = 0; i < elements.length; i++) {
    //                       const element = elements[i] as HTMLElement;
    //                       element.style.position = 'absolute';
    //                       element.style.top = `${buttonRect.top - element.offsetHeight}px`;
    //                       element.style.left = `${leftOffset}px`;
    //                   }
    //                   const iconElement = buttonElement.querySelector('button');
    //                   if (iconElement) {
    //                     iconElement.classList.add('ql-max_ai_open');
    //                     iconElement.classList.remove('ql-max_ai');
    //                   }
    //                 menu.style.visibility = 'visible';
    //               } else {
    //                   console.error('Element has zero width or height');
    //               }
    //           };
    //           const observer = new MutationObserver((mutationsList, observer) => {
    //               checkElementVisibility();
    //               observer.disconnect();
    //           });
    //           observer.observe(document.body, { childList: true, subtree: true });
    //           setTimeout(checkElementVisibility, 100);
    //           document.addEventListener('click', (event) => {
    //               const iconElement = buttonElement.querySelector('button');
    //               if (iconElement) {
    //                 iconElement.classList.remove('hcl-icon-max-ai-close');
    //                 iconElement.classList.add('hcl-icon-max-ai-open');
    //               }
    //               const overlayContainer = document.querySelector('.maxAI-menu');
    //               if (overlayContainer) {
    //                 overlayContainer.remove();
    //               }
    //           }, { capture: true });
    //       } else {
    //           console.error('Button element or overlay element not found or is hidden');
    //       }
    //   } else {
    //       console.error('No elements found with the specified class');
    //   }
    // }, 100);
    this.currentRangeForSelectedTextAI = this.quillEditorComponent.quillEditor.getSelection();
    const text = this.quillEditorComponent.quillEditor.getText(this.currentRangeForSelectedTextAI.index, this.currentRangeForSelectedTextAI.length);
    this.ngjs.onAIClick.next({
      callbackFn: this.saveTextAI,
      currentRef: this,
      text: text,
      rewriteOption: undefined
    });
    //console.log('text', text);
  }
  saveTextAI(currentRef, text) {
    if (currentRef.currentRangeForSelectedTextAI) {
      currentRef.quillEditorComponent.quillEditor.setSelection(currentRef.currentRangeForSelectedTextAI.index, currentRef.currentRangeForSelectedTextAI.length, Quill.sources.SILENT);
    }
    if (currentRef.quillEditorComponent.quillEditor.getFormat().customLink) {
      const customLink = currentRef.quillEditorComponent.quillEditor.getFormat().customLink;
      document.querySelector(`[data-id="${customLink.id}"]`).textContent = text;
    } else {
      currentRef.quillEditorComponent.quillEditor.deleteText(currentRef.currentRangeForSelectedTextAI.index, currentRef.currentRangeForSelectedTextAI.length);
      currentRef.quillEditorComponent.quillEditor.insertText(currentRef.currentRangeForSelectedTextAI.index, text);
    }

    currentRef.quillEditorComponent.quillEditor.setSelection(currentRef.currentRangeForSelectedTextAI.index, text.length, Quill.sources.SILENT);
    currentRef.currentRangeForSelectedTextAI.length = text.length;
    currentRef.quillEditorComponent.quillEditor.theme.tooltip.hide();
  }

  getQuillFormats(): string[] {
    return [
      'bold',
      'italic',
      'underline',
      'strike',
      'blockquote',
      'code-block',
      'header',
      'list',
      'script',
      'indent',
      'direction',
      'size',
      'color',
      'background',
      'font',
      'align',
      'link',
      'customLink',
      'autosuggest',
      'personalization',
      'editortextblock'
      // 'image',
      // 'video'
    ];
  }

  getQuillConfig(showTags: boolean) {
    const container: QuillToolbarConfig = [
      ['bold', 'italic', 'underline', 'strike'],
      //https://stackoverflow.com/questions/42068335/quill-js-color-textbox (explicitly set the colors)
      [{ align: [] }, { color: ["#000000", "#e60000", "#ff9900", "#ffff00", "#008a00", "#0066cc", "#9933ff", "#ffffff", "#facccc", "#ffebcc", "#ffffcc", "#cce8cc", "#cce0f5", "#ebd6ff", "#bbbbbb", "#f06666", "#ffc266", "#ffff66", "#66b966", "#66a3e0", "#c285ff", "#888888", "#a10000", "#b26b00", "#b2b200", "#006100", "#0047b2", "#6b24b2", "#444444", "#5c0000", "#663d00", "#666600", "#003700", "#002966", "#3d1466", 'custom-color'] }, 'link', ...(showTags ? ['rule'] : [])],
      // [{ color: [] }, { background: [] }, { direction: 'rtl' }],
      ['clean']
    ];

    if (this.maxAITextFieldActive) {
      container.push(['max_ai']);
    }

    if (showTags) {
      const placeholder = Array.from(this.ngjs.MergeTags);

      if (placeholder.length) {
        // @ts-ignore
        container.splice(-1, 0, [{ placeholder }]);
      }
    }

    return {
      toolbar: {
        container,
        handlers: {
          placeholder: this.paceHolderHandler.bind(this),
          link: this.hyperLinkHandler.bind(this),
          rule: this.ruleHandler.bind(this),
          max_ai: this.aiHandler.bind(this)
        }
      },
      keyboard: {
        bindings: {
          exitBlockWithEnter: {
            key: 'enter',
            format: ['customLink'],
            handler: function (range, context) {
              if (context.suffix === "" || context.prefix === "") {
                this.quill.formatText(range.index, range.length, { customLink: null });
                this.quill.insertText(range.index, "\n");
                return false;
              }
            }
          }
        }
      }
    }
  }

  // this function will return a string after removing the enter key based on browser.
  browserCheck() {
    const innerText = this.quillEditorComponent.quillEditor.keyboard.quill.editor.scroll.domNode.innerText;
    const userAgentString = navigator.userAgent;
    const IExplorerAgent = userAgentString.indexOf("MSIE") > -1 || userAgentString.indexOf("rv:") > -1;
    let textString = '';
    if (IExplorerAgent) {
      textString = innerText.split('\r\n\r\n').join(' ');
    }
    else {
      textString = innerText.split('\n\n').join(' ');
    }
    return textString
  }
  ruleHandler() {
    const range = this.quillEditorComponent.quillEditor.getSelection();
    const text = this.quillEditorComponent.quillEditor.getText(range.index, range.length);
    const allLinks: any = new DOMParser().parseFromString(this.field.innerText, 'text/html').querySelectorAll('a');
    let fullHTML = this.quillEditorComponent.quillEditor.getText().split('\n').join(' ');
    const linkLocations = {};
    let oldStopIndex = 0;
    if (range.index > 0) {
      const firstPartHtml = fullHTML.slice(0, range.index);
      let secondPartHtml = fullHTML.slice(range.index);
      secondPartHtml = secondPartHtml.replace(text, '~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~');
      fullHTML = firstPartHtml + secondPartHtml;
    } else {
      fullHTML = fullHTML.replace(text, '~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~');
    }
    const startIndex = extractTextFromHTML(fullHTML).indexOf('~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~', oldStopIndex);
    const stopIndex = startIndex + (text.length - 1);
    fullHTML = fullHTML.replace('~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~', text);
    oldStopIndex = startIndex + (text.length);
    const dataId = this.quillEditorComponent.quillEditor.getContents(range.index, range.length).ops[0]?.attributes?.customLink?.id;
    const linkTagAlreadyExists = [...allLinks].find(x => {
      if (x.getAttribute('data-id') === dataId)
        return x;
    });
    if (linkTagAlreadyExists) {
      linkLocations[startIndex + '-' + stopIndex] = linkTagAlreadyExists.outerHTML;
      this.ngjs.addEditRuleBuilder.next({
        hyperlinkInfo: {
          id: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').getAttribute('data-id'),
          startIndex: startIndex,
          endIndex: stopIndex,
          linkText: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').innerHTML
        }
      });
    } else {
      this.modalService.openAlertDialog(this.translate.instant('messages.no-link-text'),
        this.translate.instant('ADD_CONTENT.LABEL.DYNAMIC_CONTENT'),
        this.translate.instant('COMMUNICATION.EMAIL_EDITOR.BUTTONS.OK'));
    }
  }
  hyperLinkHandler() {
    const range = this.quillEditorComponent.quillEditor.getSelection();
    const text = this.quillEditorComponent.quillEditor.getText(range.index, range.length);
    const allLinks: any = new DOMParser().parseFromString(this.field.innerText, 'text/html').querySelectorAll('a');
    let fullText = extractTextFromHTML(this.field.innerText);
    let fullHTML = this.quillEditorComponent.quillEditor.getText().split('\n').join(' ');
    const linkLocations = {};
    let oldStopIndex = 0;
    if (range.index > 0) {
      const firstPartHtml = fullHTML.slice(0, range.index);
      let secondPartHtml = fullHTML.slice(range.index);
      secondPartHtml = secondPartHtml.replace(text, '~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~');
      fullHTML = firstPartHtml + secondPartHtml;
    } else {
      fullHTML = fullHTML.replace(text, '~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~');
    }
    const startIndex = extractTextFromHTML(fullHTML).indexOf('~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~', oldStopIndex);
    const stopIndex = startIndex + (text.length - 1);
    fullHTML = fullHTML.replace('~!~!~!~!~!~TAG_START~!~!~!~!~!~' + text + '~!~!~!~!~!~TAG_END~!~!~!~!~!~', text);
    oldStopIndex = startIndex + (text.length);
    const dataId = this.quillEditorComponent.quillEditor.getContents(range.index, range.length).ops[0]?.attributes?.customLink?.id;
    const linkTagAlreadyExists = [...allLinks].find(x => {
      if (x.getAttribute('data-id') === dataId)
        return x;
    });
    if (linkTagAlreadyExists) {
      linkLocations[startIndex + '-' + stopIndex] = linkTagAlreadyExists.outerHTML;
      this.currentRangeForSelectedLink = range;
      this.ngjs.onHyperLinkClick.next({
        callbackFn: this.saveHyperLink,
        currentRef: this,
        text: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').href,
        type: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').getAttribute('data-type'),
        name: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').getAttribute('data-name'),
        id: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').getAttribute('data-id'),
        newWindow: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').getAttribute('target') == '_blank' ? true : false,
        aliasName: new DOMParser().parseFromString(linkLocations[range.index + '-' + (range.index + range.length - 1)], 'text/html').querySelector('a').getAttribute('data-alias')
      });
    } else {
      this.ngjs.onHyperLinkClick.next({
        callbackFn: this.saveHyperLink,
        currentRef: this,
        text: '',
        type: '',
        name: '',
        newWindow: '',
        aliasName: '',
        id: Utility.generateId()
      });
    }
  }

  saveHyperLink(currentRef, url: any, type: string, name: string, id: string, target: boolean, aliasName: string) {
    // currentRef.quillEditorComponent.quillEditor.format('link', url);
    if (currentRef.currentRangeForSelectedLink)
      currentRef.quillEditorComponent.quillEditor.setSelection(currentRef.currentRangeForSelectedLink.index, currentRef.currentRangeForSelectedLink.length, Quill.sources.SILENT);
    currentRef.quillEditorComponent.quillEditor.format('customLink', { type, link: url, name, id: id ? id : Utility.generateId(), newWindow: target, aliasName: aliasName});
    currentRef.quillEditorComponent.quillEditor.theme.tooltip.hide();
  }
  /**
   * This is a handler that will be called when the place holder (Personlization)
   * field is selected by the user
   */
  private paceHolderHandler(selector: string): void {
    // this.quillEditorComponent.addClasses('')
    const range = this.quillEditorComponent.quillEditor.getSelection();
    // to fetch the immediate charcter before the selector
    /*const strBeforeCurrentSelector = this.quillEditorComponent.quillEditor.getText(range.index-1, 1);
    const text = this.quillEditorComponent.quillEditor.getText(range.index, range.length);
    this.quillEditorComponent.quillEditor.deleteText(range.index, text.length);
       if(strBeforeCurrentSelector!== " " ){
         this.insertEmptySpace(range.index );
         range.index = range.index + 1;
       } */
    // les generate a ID
    const id: string = this.ngjs.MergeTagsConfig.generateId(selector)
    //  this.quillEditorComponent.quillEditor.insertEmbed(range.index, 'personalization',{id: id, text: selector});
    this.insertPersonalization({
      label: selector
    }, range, true);
    // since we have added this we have to emit a event as well
    //  this.quillEditorComponent.quillEditor.setSelection(range.index, selector.length);
    //  this.insertEmptySpace(range.index + selector.length );
    this._field.innerText = this.quillEditorComponent.editorElem.querySelector('.ql-editor').innerHTML;
  }

  private insertEmptySpace(index: number): void {
    const delta: any[] = [];
    // if index is 0, then no retain is required
    if (index > 0) {
      delta.push({
        retain: index
      });
    }
    delta.push({
      insert: ' '
    });
    this.updateTextChangeFlag(false);
    this.quillEditorComponent.quillEditor.updateContents(delta);
  }

  updateTextChangeFlag(flag: boolean) {
    this.ngjs.executeTextChange = flag
  }

  public contentChanged(event: any): void {
    this.field.innerText = event.html;
    // update rules if hyperlinks have been deleted or modified
    this.updateHyperlinkRules();
    this.ngjs.validateLandingPages();
  }

  updateHyperlinkRules() {
    let allLinks: any = new DOMParser().parseFromString(this.field.innerText, 'text/html').querySelectorAll('a');
    allLinks = [...allLinks];
    if (allLinks?.length > 0) {
      // extract all link ids and their text
      const allLinksArray = allLinks.map(x => {
        return {
          id: x.getAttribute('data-id'),
          text: x.innerHTML
        }
      });
      let linkObject = null;
      // filter the rules - only those rules for which hyperlink actully exists. also
      // update link text
      this.field.options.rules = this.field?.options?.rules?.filter((rule) => {
        linkObject = allLinksArray.find(y => y.id === rule.linkId);
        if (linkObject) {
          rule.linkText = linkObject.text;
          return true;
        }
        return false;
      });
    } else {
      this.field.options.rules = [];
    }
    this.ngjs.getHyperLinks("landingPage", false);
  }

  setRuleLengthAttribute() {
    if (this.field.innerText && this.ngjs.executeTextChange) {
      const domObject = new DOMParser().parseFromString(this.field.innerText, 'text/html');
      const allLinks: any = domObject.querySelectorAll('a');
      let ruleLength: number = 0;
      // let linkObject = null;
      if (allLinks && allLinks.length > 0) {
        allLinks.forEach((elem) => {
          if (elem.getAttribute('data-id')) {
            // linkObject = this.ngjs.hyperlink?.find(x => x.id === elem.getAttribute('data-id'));
            // if (linkObject) {
            //   ruleLength = linkObject?.rules.length;
            // } else {
            //   ruleLength = 0;
            // }
            ruleLength = this.field.options.rules.reduce((a, c) => c.linkId === elem.getAttribute('data-id') ? a + 1 : a, 0);
            elem.setAttribute('data-rulelength', ruleLength);
          }
        });
        this.field.innerText = domObject.body.innerHTML;
        this.html = this.field.innerText;
      }
    }
  }

  public getPlaceholder() {
    return this.translate.instant('settings.insert_text_here');
  }

  /**
   * when editor is instantiated.
   * param quill, reference to quill editor
   */
  public editorCreated(event) {
    // since the editor is created, lets attach the approprite listeners to listen to key strokes
    this.initEditorEventHandlers();
  }

  /**
   * Initailize editor event handlers.
   */
  initEditorEventHandlers(): void {
    this.quillEditorComponent.quillEditor.root.addEventListener("keydown", this.keyDownEventHandler.bind(this));
    this.quillEditorComponent.quillEditor.root.addEventListener("click", this.mouseDownEventHandler.bind(this));
    this.quillEditorComponent.quillEditor.root.addEventListener("paste", this.pasteEventHandler.bind(this));
  }

  /**
   * Editor paste handler
   * @param evt
   */
  pasteEventHandler(evt): void {
    setTimeout(() => {
      this.resetLastTypedChar();
    }, 200);
  }


  /**
 * Whenever user hits a key on the editor this function will be called
 * param event
 * When user clicks on PF node, open menu
 */
  private mouseDownEventHandler(event): void {
    const clickedElement = event.target;
    if (clickedElement.classList.contains('droppable') && !(clickedElement.classList.contains('link-droppable'))) {
      this.personalizationElementClicked(event);
    }
  }

  /**
 * Whenever user hits a key on the editor this function will be called
 * param event
 */
  private keyDownEventHandler(event): void {
    var e = window.event ? event : e;

    if (((e.shiftKey && e.key === '#') || e.key === '#') && (this.lastTypedChar !== '#') && !this.ngjs.isStaticLP) {
      this.insertAutoSuggestNode();
    }

    switch (e.keyCode) {
      case EditorEvents.ESC.key:
        this.closeMenu(event);
        break;

      case EditorEvents.ARROWUP.key:
        this.traversePreviousItem(event);
        break;

      case EditorEvents.ARROWDOWN.key:
        this.traverseNextItem(event);
        break;

      case EditorEvents.ENTER.key:
        this.selectPersonalization();
        break;

      case EditorEvents.DELETE.key:
        this.deletePersonalizationFields();
        break;

      case EditorEvents.SPACE.key:
        this.resetLastTypedChar();
        break;

      case EditorEvents.TAB.key:
        this.resetLastTypedChar();
        break;
    }
  }

  /**
 * Called when user clicks on a personalization field
 */
  private personalizationElementClicked(event): void {
    // user has clicked on a personalization field, lets select the personalization
    // field
    const selectedRange = this.quillEditorComponent.quillEditor.getSelection();
    const isSafari = /Safari/.test(navigator.userAgent) && /Apple Computer/.test(navigator.vendor);
    if (isSafari) {
      event.target.classList.add('read-write');
    }
    const blot = Parchment.find(event.target);
    //   if (blot instanceof TextNode) {
    const index = this.quillEditorComponent.quillEditor.getIndex(blot);
    this.quillEditorComponent.quillEditor.setSelection(index, blot.domNode.innerText.length, Quill.sources.SILENT);
    //   }
    this.textNode = blot;
    this.rangeToInsertPersonalization = this.getRange();
    this.updatePersonalizationFieldAction = true;
    this.openMenu(event, false, blot.domNode);
  }


  /**
* Inserts a new blot 'autosuggest', that keeps a track of
* index / position of where the # symbol is typed.
* Based on this blot / node, we open pop-up / insert
* Personalization fields at given index.
*
*/

  insertAutoSuggestNode(): void {
    /**
      * # implementation and autosuggest thing .
        Can try this :- Detect newly blot position and append an input to this blot, where
        you keep adding the newly added characters to this input, so that user feels he is adding on
        editor and proceed.
      */
    let range = this.getRange();
    const rangeIndex = range.index,
      rangeLength = range.length;

    // If selected text, delete text and calc new range post deletion.
    if (rangeLength) {
      this.deleteText(rangeIndex, rangeLength);
      range = this.getRange();
    }

    this.rangeToInsertPersonalization = range;
    const tmp: any[] = [];

    // if index is 0, then no retain is required

    if (rangeIndex > 0) {
      tmp.push({
        retain: rangeIndex
      });
    }
    tmp.push({
      insert: {
        autosuggest: { label: ' ', messageBody: '', length: 0 }
      }
    });
    this.updateTextChangeFlag(false);
    this.quillEditorComponent.quillEditor.updateContents(tmp);

    const self = this;
    setTimeout(function (event) {
      const caretNode = Parchment.find(document.querySelector('.autosuggest')),
        caretNodeDom = caretNode.domNode;
      self.openMenu(event, true, caretNodeDom);
    }, 100);
  }

  public getRange() {
    let range = this.quillEditorComponent.quillEditor.getSelection();
    if (!range) {
      range = {
        index: this.quillEditorComponent.quillEditor.getLength() - 1,
        length: 0
      };
    }
    return range;
  }

  /**
* Maps the PF List with known format.
*/
  private getUpdatedPersonalizationList(personalizationList): { key: string, value: string, prefix: string }[] {
    personalizationList.forEach((PFItem: any) => {
      PFItem.label = PFItem.key;
    });
    return personalizationList;
  }


  /**
 * Gets Personalization List Details.
 * @param personalizationList
 * @returns
 */
  public getpersonalizationList(): void {
    const personalizationFieldData = this.ngjs.personalizedTagsFull.data;
    this.personalizationList = this.getUpdatedPersonalizationList(personalizationFieldData);
    this.origpersonalizationList = Array.from(personalizationFieldData);
  }

  /**
   * Sets data and event handlers
   *  for auto-suggest mode.
  */

  private autoSuggestModeConfig(): void {
    this.isAutoSuggestMode = true;
    this.resetLastTypedChar();
    this.charAfterSpecialSymbol = '';
    this.autoSuggestCharList.push('#');
    this.initAutoSuggestModeEventHandlers();
  }

  /**
 * Register Event handlers for
 * Auto Suggest Mode.
 */
  private initAutoSuggestModeEventHandlers(): void {
    this.quillEditorComponent.quillEditor.root.addEventListener("keydown", this.getTypedChar, false);
  }

  /**
   * Opens the Personalization fields pop-up.
   * @param event
   * @param autoSuggestMode : Is autosuggest on
   * @param caretNodeDom : curosr Node, where # is typed
   */

  private openMenu(event: any, autoSuggestMode: boolean, caretNodeDom?: HTMLElement) {
    // Reset the highlight item counter.
    this.traversedItemCount = -1;
    this.isMenuOpened = false;
    this.isMenuOpened = true;

    if (autoSuggestMode) {
      this.autoSuggestModeConfig();
    }
    const suggestionMenuitem = document.querySelector('ul.suggestionList');
    suggestionMenuitem.addEventListener("mousedown", this.mouseDownHandler, false);
    document.addEventListener("mousedown", this.closeMenu, false);
    this.caretNodeDom = caretNodeDom;
    const self = this;
    this.createMenu();
    setTimeout(function (event) {
      self.positionMenu();
    }, 100);
  }

  /**
   * Create PF menu
   */
  createMenu(): void {
    const suggestionMenuitem = document.querySelector('ul.suggestionList');
    suggestionMenuitem.innerHTML = '';
    for (let i = 0; i < this.personalizationList.length; i++) {
      let liItem = document.createElement('li');
      const item = this.personalizationList[i];
      liItem.title = item.value;
      liItem.setAttribute("data-label", item.label);
      liItem.setAttribute("data-data", item.value);
      liItem.setAttribute("data-count", i + '');
      liItem.className = 'listItem';
      let spanItem = document.createElement('span');
      spanItem['innerHTML'] = item.value;
      liItem.appendChild(spanItem);
      suggestionMenuitem.appendChild(liItem);
    }
  }

  /**
 * Delete Personalization fields, when back space key
 * is pressed.
 */

  deletePersonalizationFields(): void {
    this.resetLastTypedChar();
    // Focus editor, IE issue fix
    this.quillEditorComponent.quillEditor.focus();
    const selectedRange = this.quillEditorComponent.quillEditor.getSelection();
    const isIE11 = !!window.MSInputMethodContext && !!document.DOCUMENT_NODE;
    if (selectedRange) {
      const format = this.quillEditorComponent.quillEditor.getFormat(selectedRange.index);
      if (format && format.editortextblock) {
        let blot = this.quillEditorComponent.quillEditor.getLeaf(selectedRange.index);
        if (!isIE11) {
          blot[0].domNode.parentElement.classList.add('read-write');
        }
        blot = Parchment.find(blot[0].domNode);
        this.quillEditorComponent.quillEditor.setSelection(selectedRange.index - blot.text.length, blot.text.length, Quill.sources.SILIENT);
      }
    }
  }

  /**
* Traverse the previous item.
*/

  private traversePreviousItem(event): void {
    event.preventDefault();
    this.traversedItemCount--;
    if (this.traversedItemCount < -1) {
      this.traversedItemCount = -1;
    }
    this.highLightItem();
  }

  /**
   *  on key down event on PF list,
   *  traverse next item in list.
   */
  private traverseNextItem(event): void {
    event.preventDefault();
    this.traversedItemCount++;
    if (this.traversedItemCount === this.personalizationList.length) {
      this.traversedItemCount--;
      return;
    }
    this.highLightItem();
  }

  /**
   * Selects a node text.
   */
  private selectItem(currNode) {
    const node: HTMLInputElement = currNode.firstChild.firstChild;
    const textLen = currNode.innerText.length;
    var range = document.createRange();
    range.setStart(node, 0);
    range.setEnd(node, textLen);
    var selection = window.getSelection();
    selection.removeAllRanges();
    const isIE11 = !!window.MSInputMethodContext && !!document.DOCUMENT_NODE;
    if (!isIE11) {
      selection.addRange(range); // IE 11, key up / down not working on pop up menu.
    }
  }

  /**
* Removes the high-lighed class from the items.
*/

  private unHighLightItems(): void {
    const listItemsCount = this.personalizationList.length;
    for (let index = 0; index < listItemsCount; index++) {
      const currentItem = document.querySelector('li[data-count="' + index + '"' + "]");
      currentItem && currentItem.classList.remove('highlight');
    }
  }

  /**
   * High Light current Item.
   * @param event
   */

  private highLightItem(): void {
    this.unHighLightItems();
    this.currentTraversedElement = document.querySelector('li[data-count="' + this.traversedItemCount + '"' + "]") as HTMLElement;
    // Key down / up on menu conatiner, makes list items in view when scroll is there.
    this.currentTraversedElement.parentElement.scrollTop = this.currentTraversedElement.offsetTop - this.currentTraversedElement.offsetHeight;
    this.selectItem(this.currentTraversedElement);
    this.currentTraversedElement.classList.add('highlight');
  }

  /**
   * Gets typed char in editor, used for autosuggest mode
   * @param event
   */
  private getTypedChar(event): void {
    var e = window.event ? event : e;
    // To try :- Create a hidden input box and provide a class than autosuggest.
    const inputChar = String.fromCharCode(event.keyCode).toLowerCase();

    // check for underscore key
    if (e.shiftKey && e.key === '_') {
      this.autoSuggestCharList.push(this.charAfterSpecialSymbol);
      this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + '_';
    }

    // Check for input was a letter, number, hyphen, underscore or space .
    // alphanumeric hash space , delete key check.
    if (/[a-z0-9-_ ]/.test(inputChar) || event.keyCode === 8) {
      if (/[a-z0-9-_ ]/.test(inputChar)) { // all but delete key press
        this.autoSuggestCharList.push(this.charAfterSpecialSymbol);
        if (e.shiftKey && e.key === '#') {
          this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + e.key;
        } else {
          this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + String.fromCharCode(e.keyCode).toLowerCase();
        }
      } else { // delete key press
        this.autoSuggestCharList.pop();
        this.charAfterSpecialSymbol = this.charAfterSpecialSymbol.slice(0, -1);
      }

      const vm = this;
      this.personalizationList = this.origpersonalizationList.filter(function (listItem) {
        // use includes
        return (listItem.key.toLowerCase()).indexOf(vm.charAfterSpecialSymbol) >= 0;
      });
      console.log("charAfterSpecialSymbol::", this.charAfterSpecialSymbol);
      this.lastTypedChar = this.charAfterSpecialSymbol;
      this.createMenu();
      if (this.personalizationList && !this.personalizationList.length) {
        this.closeMenu();
      }
      // check if user deletes # when no char is there,
      if ((this.charAfterSpecialSymbol === '') && !this.autoSuggestCharList.length) {
        this.closeMenu();
      }
    }
  };

  /**
 * Closes the pop-up menu
 */
  public closeMenu(event?) {
    // Because of event bubbling, click registered on document bubbles,
    // when PF item in pop-up is  clicked

    this.updateTextChangeFlag(true);
    this.traversedItemCount = -1;
    this.currentTraversedElement = null;
    if (event && event.target.classList.contains('mat-menu-panel') || event && event.target.classList.contains('suggestionList') || event && event.target.getAttribute('data-label')) {
      return;
    }
    this.postPopUpCloseCallback();
  }

  /**
* Callback to run when Personalization list is closed.
* Reset data and listeners
*/
  postPopUpCloseCallback(): void {
    this.charAfterSpecialSymbol = '';
    this.personalizationList = this.origpersonalizationList;
    this.isAutoSuggestMode = false;
    this.currentTraversedElement = null;
    this.autoSuggestCharList = [];

    const autoSuggestElement = document.querySelectorAll(".autosuggest");
    // remove the span having autosuggest class, for next pop-up positioning.
    for (let i = 0; i < autoSuggestElement.length; i++) {
      const spanText = (autoSuggestElement[i] as HTMLElement).innerText,
        parentNode = autoSuggestElement[i].parentNode;
      // autosuggest node at line start, has some text content. Clone autosuggest node content.
      if ((autoSuggestElement[i] as HTMLElement).innerText.trim()) {
        const _span = document.createElement('span');
        _span.innerText = spanText.trim();
        _span.style.backgroundColor = 'white';
        parentNode.insertBefore(_span, autoSuggestElement[i]);
      }
      parentNode.removeChild(autoSuggestElement[i]);
    }

    this.quillEditorComponent.quillEditor.root.removeEventListener("keydown", this.getTypedChar, false);
    const suggestionMenuitem = document.querySelector('ul.suggestionList');
    suggestionMenuitem.removeEventListener("mousedown", this.mouseDownHandler, false);
    document.removeEventListener("mousedown", this.closeMenu, false);
    const menuPanel = document.querySelector('.suggestionListContainer') as HTMLElement;
    menuPanel.style.top = "-100000px";
    menuPanel.style.left = "-100000px";
    if (this.updatePersonalizationFieldAction) {
      this.updatePersonalizationFieldAction = false;
    }
    this.updateTextChangeFlag(true);
  }


  /**
   * Positions the pop-up based on caret / cursor position.
   * which in turn depends on 'autosuggest' blot node .
  */

  private positionMenu(): void {
    const menuPanel = document.querySelector('.suggestionListContainer') as HTMLElement;
    const listContainer = document.querySelector('.suggestionList') as HTMLElement;
    const careNodePosition = this.caretNodeDom.getBoundingClientRect();
    // position the pop-up.
    menuPanel.style.top = (careNodePosition.top + careNodePosition.height) + 'px';
    menuPanel.style.left = (careNodePosition.left - 65) + 'px';
    const isElementInViewport = this.isElementInViewport(menuPanel);
    const rect = menuPanel.getBoundingClientRect();
    if (!isElementInViewport) { // If element outside viewport, position the menu list upward.
      menuPanel.style.top = (rect.top - (rect.height + careNodePosition.height)) + 'px';
    }
  }

  /**
   * checks is element is in view port
   * param: element, to check.
   */
  private isElementInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  /**
   * This function will add a personalization filed at the specified
   * location
   */
  public insertPersonalization(personalization: {
    label: string, id?: any
  },
    range?: { index: number, length: number }, textSelected?: boolean): void {
    // les generate a ID
    const id: string = this.ngjs.MergeTagsConfig.generateId(personalization.label);
    // add prefix symbol, when text is not selected. For inserting PF from custom menu.
    if (!textSelected) {
      personalization.label = '<--' + personalization.label + '-->';
    }
    personalization.id = id;
    // if range is not provided, we will add at the current cursor position
    if (!range) {
      range = this.getRange();
    }
    // add the editortextblock
    // make sure we delte the selected text
    if (range.length > 0) {
      this.deleteText(range.index, range.length);
    }
    // For updation, donot insert space.
    if (!this.updatePersonalizationFieldAction) {
      this.insertEmptySpace(range.index);
    }
    // insert Personlization in timeout
    setTimeout(() => {
      const tmp: any[] = [];
      // if index is 0, then no retain is required
      if (range.index > 0) {
        tmp.push({
          retain: range.index
        });
      }
      tmp.push({
        insert: {
          editortextblock: personalization
        }
      });
      this.updateTextChangeFlag(true);
      this.quillEditorComponent.quillEditor.updateContents(tmp);
      this.quillEditorComponent.quillEditor.setSelection(range.index + personalization.label.length + 1, 0, Quill.sources.SILENT);
    }, 100);
  }

  /**
   * registering event on UL instead of each LI.
   * @param event
   */
  mouseDownHandler(event: any) {
    const _target = event.target;
    if (_target.nodeName && _target.nodeName.toLocaleLowerCase() === 'li' && _target.classList.contains('listItem')) {
      this.selectPersonalization(event);
    }
  }

  // Click of Personalization Field
  selectPersonalization(event?: any) {
    // tell the editor to insert this personalization
    let target;
    // select values using key board up and down arrows.
    if (this.currentTraversedElement) {
      target = this.currentTraversedElement;
    } else {
      target = event && event.target;
    }
    // new line
    if (!target) {
      this.resetLastTypedChar();
    }
    const data = target.getAttribute('data-data');
    const label = target.getAttribute('data-label');

    let length: number = target.innerText.length;

    // delete the # and search characters.
    // +1 as space char is also included.
    if (this.isAutoSuggestMode) {
      this.deleteText(this.rangeToInsertPersonalization.index, this.charAfterSpecialSymbol.length + 1);
    }

    this.insertPersonalization({
      label: label
    }, this.rangeToInsertPersonalization);

    this.postPopUpCloseCallback();
  }

  /**
* In case user wants to delete a text this function can be called for the same
* param {number} from
* param {number} length
*/
  public deleteText(from: number, length: number): void {
    this.quillEditorComponent.quillEditor.deleteText(from,
      length,
      Quill.sources.SILENT);
  }
  /**
   * This function can be used to get text user has entered in editor
   * returns
   */
  public getText(): string {
    return this.quillEditorComponent.quillEditor.getText();
  }

  /**
   * Unsubscribe all subscriptions
   */
  ngOnDestroy(): void {
    this.subscriptionList.forEach((sub: SubscriptionLike) => {
      sub.unsubscribe();
    });
  }

  /**
  * Resets last typed char
  */
  private resetLastTypedChar(): void {
    this.lastTypedChar = '';
  }

  fetchComponentErrorMessages() {
    const errorMessages = new Set();
    for (let iErrorCounter = 0; iErrorCounter < this.field.errors.length; iErrorCounter++) {
      errorMessages.add(this.translate.instant('messages.' + this.field.errors[iErrorCounter].key + '-collated'))
    }
    return `${Array.from(errorMessages).join(`\n\r`)}`;
  }
}
