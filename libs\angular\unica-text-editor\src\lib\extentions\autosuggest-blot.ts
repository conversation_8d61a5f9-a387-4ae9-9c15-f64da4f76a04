import Quill from "quill";

// Import the correct Inline blot for Quill 2.0
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const Inline = Quill.import("blots/inline") as any;

interface AutoSuggestData {
  label?: string;
  length?: number;
  messageBody?: string;
}

export class AutoSuggestNode extends Inline {
  static blotName = "autosuggest";
  static className = "autosuggest";
  static tagName = "span";
  static scope = Quill.import('parchment').Scope.INLINE; // Add scope for Quill 2.0

  // Add allowedChildren property for Quill 2.0 compatibility
  static allowedChildren: never[] = [];

  // The data object that we sent to this blot
  public contentData: any;

  // Updated constructor for Quill 2.0 - changed signature
  constructor(scroll: any, domNode: HTMLElement, value?: any) {
    super(scroll, domNode, value);
    this.contentData = value || {};
  }

  clone() {
    const obj: AutoSuggestNode = super.clone();
    obj.contentData = this.contentData;
    return obj;
  }

  /**
   * This function will actually create the blot
   * param {{label: string; length: number}} value
   * returns {HTMLElement}
   */
  static create(value?: any): HTMLElement {
    const node = super.create() as HTMLElement;

    // Ensure the node has the correct class name
    node.className = this.className;

    if (value?.label) {
      this.setNodeConfigurations(node, value);
    }
    return node;
  }

  /**
   * We will set some configurations on the node
   * param node
   * param value
   */
  static setNodeConfigurations(node: HTMLElement, value: any) {
    node.innerHTML = value.label;
    node.setAttribute("data-text", JSON.stringify(value));
    node.setAttribute("contenteditable", "false");

    // Style the autosuggest node to be invisible/transparent
    node.style.backgroundColor = 'transparent';
    node.style.border = 'none';
    node.style.outline = 'none';
    node.style.display = 'inline';
    node.style.position = 'relative';
  }

  /**
   * Returns the format on the blot - updated for Quill 2.0
   * @param node
   * @param _scroll - Added scroll parameter for Quill 2.0
   * @returns
   */
  static formats(node: HTMLElement, _scroll?: unknown) {
    const dataText = node.getAttribute("data-text");
    try {
      return dataText ? JSON.parse(dataText) : null;
    } catch {
      return dataText;
    }
  }

  /**
   * Add value method for Quill 2.0 compatibility
   */
  static value(node: HTMLElement): any {
    const dataText = node.getAttribute("data-text");
    try {
      return dataText ? JSON.parse(dataText) : { label: node.innerHTML };
    } catch {
      return { label: node.innerHTML };
    }
  }

  /**
   * Get the content data
   */
  getContentData(): any {
    return this.contentData;
  }

  /**
   * Update the content data
   */
  updateContentData(data: any) {
    this.contentData = data;
  }

  /**
   * Get the text length
   */
  getTextLength(): number {
    const domNode = (this as any).domNode as HTMLElement;
    if (domNode) {
      return domNode.innerText.length;
    }
    return 0;
  }
}
