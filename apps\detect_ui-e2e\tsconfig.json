{"extends": "../../tsconfig.base.json", "compilerOptions": {"allowJs": true, "outDir": "../../dist/out-tsc", "module": "commonjs", "types": ["cypress", "node"], "sourceMap": false, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["**/*.ts", "**/*.js", "cypress.config.ts", "**/*.cy.ts", "**/*.cy.js", "**/*.d.ts"]}