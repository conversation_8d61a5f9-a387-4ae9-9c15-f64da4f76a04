import {
  CDP_ANALYTICS_KEY,
  CDP_CAMPAIGN_KEY,
  CDP_CAMPAIGN_SUMMARY_KEY,
  CDP_CHANNEL_KEY, CDP_COHORT_KEY,
  CDP_COV_KEY,
  CDP_CUSTOMER_PROPS_KEY,
  CDP_DASHBOARD_KEY,
  CDP_DATA_PIPELINE_KEY,
  CDP_DESTINATIONS_KEY,
  CDP_EVENTS_DASHBOARD_KEY,
  CDP_EVENTS_DIRECTORY_KEY, CDP_EVENTS_KEY,
  CDP_FAV_KEY, CDP_FUNNEL_KEY,
  CDP_HOME_KEY, CDP_OCCURRENCE_KEY,
  CDP_OFFLINE_DS_KEY, CDP_PATH_KEY,
  CDP_PROFILE_MANAGE_KEY,
  CDP_PROFILE_UPLOAD_KEY,
  CDP_SEGMENTS_KEY,
  CDP_SOURCER_KEY,
  CDP_TRAFFIC_ANALYSIS_KEY, CDP_USER_JOURNEY_KEY
} from './cdp-constants';
import { UnicaSideNavSectionConfig } from '@hcl/unica-common';

/**
 * The unica side navigation sections that we have for the
 * cdp application
 */
export function CdpSideNavSectionGenerator (translation: (label: string) => string) {
  return [
    {
      // the first section for home & fav
      elements: [
        {
          id: CDP_HOME_KEY,
          icon: 'home',
          label: translation('UNICA_COMMON.LABELS.HOME'),
          key: CDP_HOME_KEY,
          order: 1
        },
        {
          id:  CDP_FAV_KEY,
          icon: 'unica_favorite',
          label: translation('UNICA_COMMON.LABELS.FAVOURITE'),
          children: [],
          order: 2,
          key: CDP_FAV_KEY
        }
      ]
    },
    {
      // the 2nd section for application specific elements
      elements: [
        {
          id: CDP_DASHBOARD_KEY,
          icon: 'unica_dashboard',
          label: translation('UNICA_COMMON.LABELS.DASHBOARD'),
          order: 1,
          key: CDP_DASHBOARD_KEY,
          path: ['dashboard','dashboard']
        },
        {
          id: CDP_COV_KEY,
          icon: 'unica_cov',
          label: translation('UNICA_CDP.LABELS.COV'),
          order: 2,
          key: CDP_COV_KEY,
          path:['dashboard','cov_basic']
        },
        {
          id: CDP_CAMPAIGN_KEY,
          icon: 'unica_cdp_campaign',
          label: translation('UNICA_CDP.LABELS.CAMPAIGNS'),
          order: 3,
          key: CDP_CAMPAIGN_KEY
        },
        {
          id: CDP_CHANNEL_KEY,
          icon: 'unica_channels',
          label: translation('UNICA_CDP.LABELS.CHANNELS'),
          order: 4,
          key: CDP_CHANNEL_KEY
        },
        {
          id: CDP_SEGMENTS_KEY,
          icon: 'unica_segments',
          label: translation('UNICA_CDP.LABELS.SEGMENTS'),
          order: 5,
          key: CDP_SEGMENTS_KEY
        },
        {
          id: CDP_DATA_PIPELINE_KEY,
          icon: 'unica_data_pipeline',
          label: translation('UNICA_CDP.LABELS.DATA_PIPE_LINE'),
          order: 6,
          key: CDP_DATA_PIPELINE_KEY,
          children: [
            {
              id:CDP_SOURCER_KEY,
              icon: 'unica_sources',
              label: translation('UNICA_CDP.LABELS.SOURCES'),
              order: 1,
              key: CDP_SOURCER_KEY
            },
            {
              id: CDP_DESTINATIONS_KEY,
              icon: 'unica_destinations',
              label:translation('UNICA_CDP.LABELS.DESTINATIONS'),
              order: 2,
              key: CDP_DESTINATIONS_KEY
            },
            {
              id:CDP_EVENTS_DIRECTORY_KEY,
              icon: 'unica_event_directory',
              label: translation('UNICA_CDP.LABELS.EVENT_DIRECTORY'),
              order: 3,
              key: CDP_EVENTS_DIRECTORY_KEY
            },
            {
              id: CDP_PROFILE_MANAGE_KEY,
              icon: 'unica_profile_management',
              label: translation('UNICA_CDP.LABELS.PROFILE_MANAGEMENT'),
              order: 4,
              key: CDP_PROFILE_MANAGE_KEY,
              children: [
                {
                  id: CDP_OFFLINE_DS_KEY,
                  icon: 'unica_offline_data_sources',
                  label: translation('UNICA_CDP.LABELS.OFFLINE_DS'),
                  order: 1,
                  key: CDP_OFFLINE_DS_KEY
                },
                {
                  id: CDP_CUSTOMER_PROPS_KEY,
                  icon: 'unica_customer_properties',
                  label: translation('UNICA_CDP.LABELS.CUSTOMER_PROPS'),
                  order: 2,
                  key: CDP_CUSTOMER_PROPS_KEY
                },
                {
                  id: CDP_PROFILE_UPLOAD_KEY,
                  icon: 'unica_profile_upload',
                  label: translation('UNICA_CDP.LABELS.PROFILE_UPLOAD'),
                  order: 3,
                  key: CDP_PROFILE_UPLOAD_KEY
                }
              ]
            }
          ]
        },
        {
          id: CDP_ANALYTICS_KEY,
          icon: 'unica_analytics',
          label: translation('UNICA_CDP.LABELS.ANALYTICS'),
          order: 7,
          key: CDP_ANALYTICS_KEY,
          children: [
            {
              id: CDP_CAMPAIGN_SUMMARY_KEY,
              icon: 'unica_destinations',
              label: translation('UNICA_CDP.LABELS.CAMPAIGN_SUMMARY'),
              order: 1,
              key: CDP_CAMPAIGN_SUMMARY_KEY
            },
            {
              id: CDP_TRAFFIC_ANALYSIS_KEY,
              icon: 'unica_destinations',
              label: translation('UNICA_CDP.LABELS.TRAFFIC_ANALYSIS'),
              order: 2,
              key: CDP_TRAFFIC_ANALYSIS_KEY
            },
            {
              id: CDP_USER_JOURNEY_KEY,
              icon: 'unica_journey',
              label: translation('UNICA_CDP.LABELS.USER_JOURNEY'),
              order: 3,
              key: CDP_USER_JOURNEY_KEY,
              children: [
                {
                  id: CDP_FUNNEL_KEY,
                  icon: 'unica_funnel',
                  label: translation('UNICA_CDP.LABELS.FUNNEL'),
                  order: 1,
                  key: CDP_FUNNEL_KEY
                },
                {
                  id: CDP_PATH_KEY,
                  icon: 'unica_path',
                  label: translation('UNICA_CDP.LABELS.PATH'),
                  order: 2,
                  key: CDP_PATH_KEY
                },
                {
                  id: CDP_COHORT_KEY,
                  icon: 'unica_cohort',
                  label: translation('UNICA_CDP.LABELS.COHORT'),
                  order: 3,
                  key: CDP_COHORT_KEY
                }
              ]
            },
            {
              id: CDP_EVENTS_KEY,
              icon: 'unica_events',
              label: translation('UNICA_CDP.LABELS.EVENTS'),
              order: 4,
              key: CDP_EVENTS_KEY,
              children: [
                {
                  id: CDP_EVENTS_DASHBOARD_KEY,
                  icon: 'unica_dashboard',
                  label: translation('UNICA_COMMON.LABELS.DASHBOARD'),
                  order: 1,
                  key: CDP_EVENTS_DASHBOARD_KEY
                },
                {
                  id: CDP_OCCURRENCE_KEY,
                  icon: 'unica_occurrence',
                  label: translation('UNICA_CDP.LABELS.OCCURRENCE'),
                  order: 2,
                  key: CDP_OCCURRENCE_KEY
                }
              ]
            }
          ]
        }
      ]
    }
  ] as UnicaSideNavSectionConfig[];
};
