import Quill from 'quill';

const Align = Quill.import('attributors/style/align');
const Direction = Quill.import('attributors/style/direction');

/**
 * Text alignment configuration for Quill editor
 * Registers custom alignment and direction attributors
 */
export default function TextAlignQuillModule() {
  try {
    // Register alignment attributor
    Quill.register(Align, true);
    
    // Register direction attributor  
    Quill.register(Direction, true);
    
    console.log('Text alignment module registered successfully');
  } catch (error) {
    console.warn('Text alignment module registration warning:', error);
  }
}
