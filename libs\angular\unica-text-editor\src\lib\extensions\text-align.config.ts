import Quill, { Parchment } from 'quill';
import { StyleAttributor } from 'parchment';

export default function TextAlignQuillModule() {
  // Define a new StyleAttributor for text alignment
  const AlignStyle = new StyleAttributor('align', 'text-align', {
    scope: Parchment.Scope.BLOCK,
    whitelist: ['right', 'center', 'justify', 'left'],
  });

  // Register the new format under the same key to override the default
  Quill.register('formats/align', AlignStyle, true);



// // https://github.com/quilljs/quill/issues/1451
// // configure Quill to use inline styles so the email's format properly
// const DirectionAttribute = Quill.import('attributors/attribute/direction');
// Quill.register('attributors/attribute/direction', DirectionAttribute, true);


// const BackgroundClass = Quill.import('attributors/class/background');
// Quill.register('attributors/class/background', BackgroundClass, true);

// const ColorClass = Quill.import('attributors/class/color');
// Quill.register('attributors/class/color', ColorClass, true);

// const DirectionClass = Quill.import('attributors/class/direction');
// Quill.register('attributors/class/direction', DirectionClass, true);

// const FontClass = Quill.import('attributors/class/font');
// Quill.register('attributors/class/font', FontClass, true);

// const SizeClass = Quill.import('attributors/class/size');
// Quill.register('attributors/class/size', SizeClass, true);


// const BackgroundStyle = Quill.import('attributors/style/background');
// Quill.register('attributors/style/background', BackgroundStyle, true);

// const ColorStyle = Quill.import('attributors/style/color');
// Quill.register('attributors/style/color', ColorStyle, true);

// const DirectionStyle = Quill.import('attributors/style/direction');
// Quill.register('attributors/style/direction', DirectionStyle, true);

// const FontStyle = Quill.import('attributors/style/font');
// Quill.register('attributors/style/font', FontStyle, true);

// const SizeStyle = Quill.import('attributors/style/size');
// Quill.register('attributors/style/size', SizeStyle, true);

// /**
//  * Lets load the quill edtor handlers
//  */
// // Import with proper typing using a type assertion
// const Inline = Quill.import('blots/inline') as typeof Quill['import']['prototype'] & {
//   scope: number;
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   create: (...args: any[]) => HTMLElement;
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   new (...args: any[]): any;
// };
// // const Embed = Quill.import('blots/block/embed');
// // const Block = Quill.import('blots/block')

// /**
//  * The class for custom links
//  */
// class CutomLink extends Inline {
//   static blotName = 'customLink';
//   static tagName = 'a';
//   static className = 'custom-link';
//   // static className: string = 'custom-link';
//   /**
//    * this will actually create the ta
//    */
//   static create(data: { type?: string, link?: string, name?: string, id?: string, rulelength?: string, newWindow?: boolean, aliasName?: string }) {
//     const node = super.create();
//     // node.classList.add('custom-link');
//     node.classList.add('droppable');
//     node.classList.add('link-droppable');
//     if (data.newWindow) {
//       node.setAttribute('target', "_blank");
//     }
//     if (data.type) {
//       node.setAttribute('data-type', data.type);
//     }
//     if (data.name) {
//       node.setAttribute('href', data.name);
//     } else {
//       node.setAttribute('href', data.link);
//     }
//     if (data.link) {
//       node.setAttribute('data-href', data.link);
//     }
//     if (data.name) {
//       node.setAttribute('data-name', data.name);
//     }
//     if (data.id) {
//       node.setAttribute('data-id', data.id);
//     }
//     if (data.rulelength) {
//       node.setAttribute('data-rulelength', data.rulelength);
//     }
//     if (data.aliasName) {
//       node.setAttribute('data-alias', data.aliasName)
//     }
//     return node;
//   }
//   /**
//    * This is mandatory to override else the code will not work
//    * The blot/inline.js file has a code
//    *    if (domNode.tagName === InlineBlot.tagName) return undefined;
//    * Due to this is we do not override this emthod it will not work
//    */
//   static formats(domNode: HTMLElement): unknown {
//     if (typeof this.tagName === 'string') {
//       return {
//         text: domNode.innerHTML,
//         link: domNode.getAttribute('data-href') || domNode.getAttribute('href'),
//         type: domNode.getAttribute('data-type'),
//         name: domNode.getAttribute('data-name'),
//         id: domNode.getAttribute('data-id'),
//         newWindow: domNode.getAttribute('target') ? true : false,
//         rulelength: domNode.getAttribute('data-rulelength'),
//         aliasName: domNode.getAttribute('data-alias')
//       };
//     }
//     return undefined;
//   }
//   /**
//    * get the data form the HTML tag
//    */
//   static value(node: HTMLElement) {
//     return {
//       text: node.innerHTML,
//       link: node.getAttribute('data-href') || node.getAttribute('href'),
//       type: node.getAttribute('data-type'),
//       name: node.getAttribute('data-name'),
//       id: node.getAttribute('data-id'),
//       newWindow: node.getAttribute('target') ? true : false,
//       rulelength: node.getAttribute('data-rulelength'),
//       aliasName: node.getAttribute('data-alias')    }  }

//   // Add scope property required for Blot constructors
//   static get scope() {
//     // Define a constant numeric value for INLINE scope
//     return 2; // 2 is the value for Scope.INLINE in parchment
//   }
// }
// Quill.register('formats/customLink', CutomLink);

// /**
//  * The class for the personalization fields
//  */
// class PersnalizationFields extends Inline {
//   static blotName = 'personalization';
//   static tagName = 'span';
//   static className = 'droppable';
//   /**
//    * this will actually create the ta
//    */
//   static create(value: { text?: string, id: string }) {
//     const node = super.create();
//     node.contentEditable = 'false';
//     node.id = value.id;
//     if (value.text) {
//       node['innerHTML'] = value.text;
//     }
//     return node;
//   }
//   /**
//    * This is mandatory to override else the code will not work
//    * The blot/inline.js file has a code
//    *    if (domNode.tagName === InlineBlot.tagName) return undefined;
//    * Due to this is we do not override this emthod it will not work
//    */
//   static formats(domNode: HTMLElement): unknown {
//     if (typeof this.tagName === 'string') {
//       return {
//         id: domNode.id
//       };
//     }
//     return undefined;
//   }
//   /**
//    * get the data form the HTML tag
//    */
//   static value(node: HTMLElement) {
//     return {
//       text: node.innerHTML,
//       id: node.getAttribute('id')    }  }

//   // Add scope property required for Blot constructors
//   static get scope() {
//     // Define a constant numeric value for INLINE scope
//     return 2; // 2 is the value for Scope.INLINE in parchment
//   }
// }
// Quill.register('formats/personalization', PersnalizationFields);
  // EdtitorTextNode and AutoSuggestNode aren't defined, so we can't register them
  // If you need these nodes, you need to define them first
}
