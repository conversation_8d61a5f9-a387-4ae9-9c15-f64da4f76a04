import { Route } from '@angular/router';
import { CdpLoginComponent } from './component/cdp-login/cdp-login.component';
import { UnicaAuthGuard } from '@hcl/angular/unica-angular-common';
import { CdpHomeComponent } from './component/cdp-home/cdp-home.component';
import { CdpDashboardComponent } from './component/cdp-dashboard/cdp-dashboard.component';

export const appRoutes: Route[] = [
  {
    path: 'login',
    component: CdpLoginComponent
  },
  {
    path: 'dataplatform',
    canActivate: [UnicaAuthGuard],
    children: [
      {
        path: 'home',
        component: CdpHomeComponent
      },
      {
        path: 'dashboard/:type',
        component: CdpDashboardComponent
      },
      {
        path: '**',
        redirectTo: 'home',
        pathMatch: 'full'
      }
    ]
  },
  {
    path: '**',
    redirectTo: 'dataplatform',
    pathMatch: 'full'
  }
  // {
  //   path: '',
  //   loadChildren: () =>
  //     import('./remote-entry/entry.routes').then((m) => m.remoteRoutes),
  // },
];
