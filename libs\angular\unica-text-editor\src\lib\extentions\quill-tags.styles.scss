/**
 * Quill Tags Picker Styles
 * Standalone styles for the Tags functionality that can be imported anywhere
 */

// Style for native Quill Tags picker - following design system
.ql-bubble {
  // Tags picker styling
  .ql-picker.ql-tags {
    color: #717182 !important;
    
    &:hover {
      color: #006075 !important;
    }
    
    .ql-picker-label {
      &::before {
        content: '🏷️ Tags';
        font-size: 12px;
      }
      
      &:hover {
        color: #006075 !important;
      }
    }
    
    .ql-picker-options {
      background-color: #fff !important;
      color: #1d1d23 !important;
      margin-top: 10px !important;
      padding: 4px 0 !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 5px #00000040 !important;
      border: 1px solid #d3d3e5 !important;
      max-height: 200px;
      overflow-y: auto;
      
      &:hover {
        color: #006075 !important;
      }
      
      .ql-picker-item {
        color: #1d1d23 !important;
        padding-left: 8px;
        padding: 8px 12px;
        font-size: 13px;
        
        // Fix for empty picker items - use data-value as content
        &::before {
          content: attr(data-value);
        }
        
        &:hover {
          background-color: #e0eeef !important;
        }
        
        &.ql-selected {
          background-color: #e0eeef !important;
          color: #006075 !important;
        }
      }
    }
  }
  
  // General toolbar picker styling consistency
  .ql-toolbar {
    .ql-picker-item {
      color: #1d1d23 !important;
      padding-left: 8px;
      
      // Fix for empty picker items - use data-value as content
      &::before {
        content: attr(data-value);
      }
      
      &:hover {
        background-color: #e0eeef !important;
      }
    }
    
    .ql-picker-label:hover {
      color: #006075 !important;
    }
    
    .ql-picker-options {
      background-color: #fff !important;
      color: #1d1d23 !important;
      margin-top: 10px !important;
      padding: 4px 0 !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 5px #00000040 !important;
      border: 1px solid #d3d3e5 !important;
      
      &:hover {
        color: #006075 !important;
      }
    }
    
    .ql-picker {
      color: #717182 !important;
      
      &:hover {
        color: #006075 !important;
      }
    }
  }
  
  // Rule button styling (if present)
  .ql-rule {
    color: #717182 !important;
    
    &:hover {
      color: #006075 !important;
    }
  }
}

// Snow theme support (if needed)
.ql-snow {
  .ql-picker.ql-tags {
    color: #717182 !important;
    
    &:hover {
      color: #006075 !important;
    }
    
    .ql-picker-label {
      &::before {
        content: '🏷️ Tags';
        font-size: 12px;
      }
      
      &:hover {
        color: #006075 !important;
      }
    }
    
    .ql-picker-options {
      background-color: #fff !important;
      color: #1d1d23 !important;
      margin-top: 10px !important;
      padding: 4px 0 !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 5px #00000040 !important;
      border: 1px solid #d3d3e5 !important;
      max-height: 200px;
      overflow-y: auto;
      
      .ql-picker-item {
        color: #1d1d23 !important;
        padding-left: 8px;
        padding: 8px 12px;
        font-size: 13px;
        
        // Fix for empty picker items - use data-value as content
        &::before {
          content: attr(data-value);
        }
        
        &:hover {
          background-color: #e0eeef !important;
        }
        
        &.ql-selected {
          background-color: #e0eeef !important;
          color: #006075 !important;
        }
      }
    }
  }
}

// Alternative color schemes (optional)
.ql-tags-theme-blue {
  .ql-picker.ql-tags {
    .ql-picker-label:hover {
      color: #1976d2 !important;
    }
    
    .ql-picker-item {
      &:hover {
        background-color: #e3f2fd !important;
      }
      
      &.ql-selected {
        background-color: #e3f2fd !important;
        color: #1976d2 !important;
      }
    }
  }
}

.ql-tags-theme-green {
  .ql-picker.ql-tags {
    .ql-picker-label:hover {
      color: #388e3c !important;
    }
    
    .ql-picker-item {
      &:hover {
        background-color: #e8f5e8 !important;
      }
      
      &.ql-selected {
        background-color: #e8f5e8 !important;
        color: #388e3c !important;
      }
    }
  }
}
