/**
 * Unica Text Editor Component Styles
 * All styles consolidated in one file for easier maintenance
 */

// Import core Quill styles directly
@import 'quill/dist/quill.core.css';
@import 'quill/dist/quill.bubble.css';

// CSS Custom Properties for theming
:host {
  // Unica Text Editor Theme Variables
  --unica-editor-border-color: #ddd;
  --unica-editor-border-radius: 4px;
  --unica-editor-padding: 8px;
  --unica-editor-min-height: 100px;
  --unica-editor-font-family: Roboto, sans-serif;
  --unica-editor-font-size: 16px;
  --unica-editor-line-height: 40px;
  --unica-editor-text-color: #000;

  // Custom Button Colors
  --unica-rule-color: lightgrey;
  --unica-rule-hover-color: #fff;
  --unica-ai-color: white;
  --unica-ai-hover-color: #d3d3d3;

  // Suggestion List
  --unica-suggestion-bg: rgba(0, 0, 0, 0.8);
  --unica-suggestion-text: #fff;
  --unica-suggestion-hover-bg: #0078d8;
  --unica-suggestion-border-radius: 20px;
  // Component layout - Host element styles
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative;
}

// Dark theme support
:host([data-theme='dark']) {
  --unica-editor-border-color: #555;
  --unica-editor-text-color: #fff;
}

.unica-text-editor-host {
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

  // Tags picker styling
  .ql-picker.ql-tags {
    color: #717182 !important;

    &:hover {
      color: #006075 !important;
    }

    .ql-picker-label {
      &::before {
        content: 'Tags';
        color: #cccccc;
        font-size: 14px;
        left: -15px;
        position: relative;
        font-weight: 500;
      }

      &:hover {
        color: #006075 !important;
      }
    }

    .ql-picker-options {
      background-color: #fff !important;
      color: #1d1d23 !important;
      margin-top: 10px !important;
      padding: 4px 0 !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 5px #00000040 !important;
      border: 1px solid #d3d3e5 !important;
      max-height: 200px;
      overflow-y: auto;

      &:hover {
        color: #006075 !important;
      }

      .ql-picker-item {
        color: #1d1d23 !important;
        padding-left: 8px;
        padding: 8px 12px;
        font-size: 13px;

        // Fix for empty picker items - use data-value as content
        &::before {
          content: attr(data-value);
        }

        &:hover {
          background-color: #e0eeef !important;
        }

        &.ql-selected {
          background-color: #e0eeef !important;
          color: #006075 !important;
        }
      }
    }
  }

  // General toolbar picker styling consistency
  .ql-toolbar {
    .ql-picker-item {
      color: #1d1d23 !important;
      padding-left: 8px;

      // Fix for empty picker items - use data-value as content
      &::before {
        content: attr(data-value);
      }

      &:hover {
        background-color: #e0eeef !important;
      }
    }

    .ql-picker-label:hover {
      color: #006075 !important;
    }

    .ql-picker-options {
      background-color: #fff !important;
      color: #1d1d23 !important;
      margin-top: 10px !important;
      padding: 4px 0 !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 5px #00000040 !important;
      border: 1px solid #d3d3e5 !important;

      &:hover {
        color: #006075 !important;
      }
    }

    .ql-picker {
      color: #717182 !important;

      &:hover {
        color: #006075 !important;
      }
    }
  }

  // Rule button styling (if present)
  .ql-rule {
    color: #717182 !important;

    &:hover {
      color: #006075 !important;
    }
  }

// Main container styles
.unica-text-editor-container {
  display: block;
  min-height: var(--unica-editor-min-height);
  width: 100%;
  height: 100%;

  // Quill Editor Customizations
  quill-editor {
    display: block;

    .ql-container {
      font-family: inherit !important;
      font-size: inherit !important;
    }

    .ql-container .ql-editor {
      direction: ltr !important;
      padding: 0 !important;
      line-height: inherit !important;
      font: inherit !important;
      overflow: visible !important;

      ul {
        display: block;
        list-style-type: disc;
        margin-top: 0px;
        margin-bottom: 0px;
        margin-block-start: 0px;
        margin-block-end: 0px;
        margin-inline-start: 0px;
        margin-inline-end: 0px;
        padding-inline-start: 40px;

        & > li {
          padding-left: 0px;
          list-style-type: default;
        }
      }
    }

    .ql-rule {
      color: var(--unica-rule-color) !important;
      z-index: 2 !important;
      padding: 1px 5px 3px 0px !important;

      &:hover {
        color: var(--unica-rule-hover-color) !important;
      }

      &::before {
        content: '\e9f2' !important;
        font-family: 'fontello' !important;
        font-style: normal !important;
        font-size: 13px !important;
        margin-left: 10px !important;
        vertical-align: middle !important;
      }
    }

    // Custom AI Buttons
    .ql-max_ai,
    .ql-max_ai_open {
      z-index: 2 !important;
      padding: 0px 5px 0 0 !important;
      color: var(--unica-ai-color) !important;

      &:hover {
        color: var(--unica-ai-hover-color) !important;
      }

      &::before {
        content: '\eda5' !important;
        font-family: 'fontello' !important;
        font-style: normal !important;
        font-size: 18px !important;
        margin-left: 8px !important;
        vertical-align: middle !important;
      }
    }

    // Bubble Theme Link Styles
    .ql-container.ql-bubble:not(.ql-disabled) a::after,
    .ql-container.ql-bubble:not(.ql-disabled) a::before {
      transform: translate(-40%, 30px) !important;
      z-index: 10 !important;
      max-width: 255px !important;
      white-space: normal !important;
      margin: 0 !important;
      padding: 15px !important;
      line-height: 18px !important;
    }

    .ql-container.ql-bubble:not(.ql-disabled) a {
      white-space: break-spaces !important;
    }

    .ql-container.ql-bubble:not(.ql-disabled) a::after {
      border-top: none !important;
      border-bottom: 6px solid black !important;
      top: 11px !important;
    }

    // Placeholder Styles
    .ql-placeholder .ql-picker-label::before {
      display: block !important;
      content: 'Tags' !important;
      min-width: 50px !important;
    }

    .ql-placeholder.ql-expanded .ql-picker-item::before {
      content: attr(data-value) !important;
      width: min-content !important;
    }

    // Tooltip Styles
    .ql-tooltip {
      z-index: 5 !important;
      line-height: normal !important;
    }

    // Toolbar Styles
    .ql-toolbar {
      width: max-content !important;
    }

    // Picker Options
    .ql-picker-options {
      overflow: auto !important;
      max-height: 200px !important;
    }
  }

  // Locale-specific styles
  quill-editor.locale_en_US {
    .ql-placeholder .ql-picker-label::before {
      content: 'Tags' !important;
    }
  }

  // Align Button Styles - Ensure align buttons are visible
  quill-editor {
    .ql-align {
      .ql-picker-label::before {
        content: '\e9e9' !important; // Default align left icon
        font-family: 'fontello' !important;
      }

      &.ql-expanded .ql-picker-label::before {
        content: '\e9e9' !important;
      }

      .ql-picker-item {
        &[data-value='center']::before {
          content: '\e9e6' !important; // Center align icon
          font-family: 'fontello' !important;
        }

        &[data-value='right']::before {
          content: '\e9e8' !important; // Right align icon
          font-family: 'fontello' !important;
        }

        &[data-value='justify']::before {
          content: '\e9e7' !important; // Justify align icon
          font-family: 'fontello' !important;
        }

        &[data-value='']::before {
          content: '\e9e9' !important; // Left align icon (default)
          font-family: 'fontello' !important;
        }
      }
    }

    // Fallback styles if fontello icons aren't available
    .ql-align .ql-picker-label,
    .ql-align .ql-picker-item {
      display: inline-block !important;
      width: auto !important;
      min-width: 24px !important;
      height: 24px !important;
      line-height: 24px !important;
      text-align: center !important;
    }
  }

  // Suggestion List
  .suggestionList {
    overflow: auto !important;
    max-height: 200px !important;
    max-width: 225px !important;
    list-style: none !important;
    font-size: 16px !important;
    letter-spacing: 0 !important;
    line-height: 28px !important;
    border-radius: var(--unica-suggestion-border-radius) !important;
    margin-bottom: 0 !important;
    padding-left: 0 !important;
    background-color: var(--unica-suggestion-bg) !important;

    .listItem {
      display: block !important;
      color: var(--unica-suggestion-text) !important;
      font-size: 14px !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      padding: 0 25px !important;

      span {
        pointer-events: none !important;
      }

      &:hover {
        cursor: pointer !important;
        background-color: var(--unica-suggestion-hover-bg) !important;
      }
    }
  }
}
