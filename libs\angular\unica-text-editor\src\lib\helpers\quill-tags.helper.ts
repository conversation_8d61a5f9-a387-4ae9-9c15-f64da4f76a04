import { QuillToolbarConfig } from 'ngx-quill';
import { QuillTagsService, TagSuggestion, TagsConfig } from './quill-tags.service';
import Quill from 'quill';

/**
 * Helper utility for easy integration of Tags functionality into any Quill editor
 * This provides a simple plug-and-play interface
 */
export class QuillTagsHelper {
  private tagsService: QuillTagsService;

  constructor(tagsService: QuillTagsService) {
    this.tagsService = tagsService;
  }

  /**
   * Easy setup method - call this to add Tags to your Quill editor
   * @param config - Tags configuration
   * @returns Object with toolbar config and handlers
   */
  setup(config: TagsConfig): {
    addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
    getHandlers: () => { [key: string]: (value: string) => void };
    onEditorCreated: (editor: Quill) => void;
  } {
    // Initialize the service
    this.tagsService.initialize(config);

    return {
      addToToolbar: (container: QuillToolbarConfig) => {
        return this.tagsService.addTagsToToolbar(container);
      },
      getHandlers: () => {
        return this.tagsService.getToolbarHandlers();
      },
      onEditorCreated: (editor: Quill) => {
        this.tagsService.setEditor(editor);
      }
    };
  }
}

/**
 * Factory function to create a QuillTagsHelper instance
 */
export function createQuillTagsHelper(tagsService: QuillTagsService): QuillTagsHelper {
  return new QuillTagsHelper(tagsService);
}




