import { QuillToolbarConfig } from 'ngx-quill';
import { QuillTagsService, TagSuggestion, TagsConfig } from '../services/quill-tags.service';
import Quill from 'quill';

/**
 * Helper utility for easy integration of Tags functionality into any Quill editor
 * This provides a simple plug-and-play interface
 * 
 * NOTE: This helper is optional - you can use QuillTagsService directly for simpler code
 */
export class QuillTagsHelper {
  private tagsService: QuillTagsService;

  constructor(tagsService: QuillTagsService) {
    this.tagsService = tagsService;
  }

  /**
   * Easy setup method - call this to add Tags to your Quill editor
   * @param config - Tags configuration
   * @returns Object with toolbar config and handlers
   */
  setup(config: TagsConfig): {
    addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
    getHandlers: () => { [key: string]: (value: string) => void };
    onEditorCreated: (editor: Quill) => void;
  } {
    // Initialize the service
    this.tagsService.initialize(config);

    return {
      addToToolbar: (container: QuillToolbarConfig) => {
        return this.tagsService.addTagsToToolbar(container);
      },
      getHandlers: () => {
        return this.tagsService.getToolbarHandlers();
      },
      onEditorCreated: (editor: Quill) => {
        this.tagsService.setEditor(editor);
      }
    };
  }

  /**
   * Quick setup with default suggestions
   */
  setupWithDefaults(onTagSelected?: (tag: TagSuggestion) => void): {
    addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
    getHandlers: () => { [key: string]: (value: string) => void };
    onEditorCreated: (editor: Quill) => void;
  } {
    const config: TagsConfig = {
      suggestions: QuillTagsHelper.getDefaultSuggestions(),
      onTagSelected,
      insertFormat: 'textblock',
      addSpace: true
    };

    return this.setup(config);
  }

  /**
   * Get default tag suggestions for common use cases
   */
  static getDefaultSuggestions(): TagSuggestion[] {
    return [
      // User Information Tags
      { id: 'user-name', display: 'User Name' },
      { id: 'user-email', display: 'User Email' },
      { id: 'user-phone', display: 'User Phone' },
      { id: 'user-company', display: 'User Company' },
      { id: 'user-title', display: 'User Title' },
      
      // Date & Time Tags
      { id: 'current-date', display: 'Current Date' },
      { id: 'current-time', display: 'Current Time' },
      { id: 'due-date', display: 'Due Date' },
      { id: 'created-date', display: 'Created Date' },
      
      // Document Tags
      { id: 'document-title', display: 'Document Title' },
      { id: 'document-id', display: 'Document ID' },
      { id: 'document-version', display: 'Document Version' },
      
      // System Tags
      { id: 'system-name', display: 'System Name' },
      { id: 'environment', display: 'Environment' },
      { id: 'application-url', display: 'Application URL' },
      
      // Custom Tags
      { id: 'project-name', display: 'Project Name' },
      { id: 'department', display: 'Department' },
      { id: 'location', display: 'Location' },
      { id: 'reference-number', display: 'Reference Number' },
      { id: 'status', display: 'Status' },
    ];
  }
}

/**
 * Factory function to create a QuillTagsHelper instance
 */
export function createQuillTagsHelper(tagsService: QuillTagsService): QuillTagsHelper {
  return new QuillTagsHelper(tagsService);
}

/**
 * Standalone function for quick integration without dependency injection
 * Use this when you want to quickly add Tags to a Quill editor
 */
export function addTagsToQuill(config: {
  suggestions: TagSuggestion[];
  onTagSelected?: (tag: TagSuggestion) => void;
  insertFormat?: 'textblock' | 'text';
  addSpace?: boolean;
}): {
  addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
  getHandlers: () => { [key: string]: (value: string) => void };
  onEditorCreated: (editor: Quill) => void;
} {
  const tagsService = new QuillTagsService();
  const helper = new QuillTagsHelper(tagsService);
  
  return helper.setup({
    suggestions: config.suggestions,
    onTagSelected: config.onTagSelected,
    insertFormat: config.insertFormat || 'textblock',
    addSpace: config.addSpace !== false
  });
}

/**
 * Quick setup function with default suggestions
 */
export function addDefaultTagsToQuill(onTagSelected?: (tag: TagSuggestion) => void): {
  addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
  getHandlers: () => { [key: string]: (value: string) => void };
  onEditorCreated: (editor: Quill) => void;
} {
  return addTagsToQuill({
    suggestions: QuillTagsHelper.getDefaultSuggestions(),
    onTagSelected
  });
}

/**
 * Example usage patterns for documentation
 */
export const USAGE_EXAMPLES = {
  // Example 1: Using with dependency injection
  withDI: `
    constructor(private tagsService: QuillTagsService) {}
    
    ngOnInit() {
      const helper = createQuillTagsHelper(this.tagsService);
      const tags = helper.setupWithDefaults((tag) => {
        console.log('Tag selected:', tag);
      });
      
      // In getQuillConfig():
      const container = [['bold', 'italic']];
      tags.addToToolbar(container);
      
      return {
        toolbar: {
          container,
          handlers: tags.getHandlers()
        }
      };
      
      // In onEditorCreated():
      tags.onEditorCreated(editor);
    }
  `,

  // Example 2: Standalone usage
  standalone: `
    ngOnInit() {
      const tags = addDefaultTagsToQuill((tag) => {
        this.hashtagSelected.emit(tag);
      });
      
      // In getQuillConfig():
      const container = [['bold', 'italic']];
      tags.addToToolbar(container);
      
      return {
        toolbar: {
          container,
          handlers: tags.getHandlers()
        }
      };
      
      // In onEditorCreated():
      tags.onEditorCreated(editor);
    }
  `,

  // Example 3: Custom suggestions
  custom: `
    ngOnInit() {
      const customSuggestions = [
        { id: 'name', display: 'Full Name' },
        { id: 'email', display: 'Email Address' }
      ];
      
      const tags = addTagsToQuill({
        suggestions: customSuggestions,
        onTagSelected: (tag) => this.onTagSelected(tag),
        insertFormat: 'text', // or 'textblock'
        addSpace: true
      });
      
      // Rest same as above...
    }
  `
};
