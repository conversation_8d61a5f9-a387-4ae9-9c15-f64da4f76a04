import { Injectable } from '@angular/core';
import { CdpUserService } from './cdp-user.service';
import { map, tap } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  CDP_ANALYTICS_KEY,
  CDP_CAMPAIGN_KEY,
  CDP_CAMPAIGN_SUMMARY_KEY,
  CDP_CHANNEL_KEY,
  CDP_COHORT_KEY,
  CDP_COV_KEY,
  CDP_CUSTOMER_PROPS_KEY,
  CDP_DASHBOARD_KEY,
  CDP_DATA_PIPELINE_KEY,
  CDP_DESTINATIONS_KEY,
  CDP_EVENTS_DASHBOARD_KEY,
  CDP_EVENTS_DIRECTORY_KEY,
  CDP_EVENTS_KEY,
  CDP_FAV_KEY,
  CDP_FUNNEL_KEY,
  CDP_HOME_KEY,
  CDP_OCCURRENCE_KEY,
  CDP_OFFLINE_DS_KEY,
  CDP_PATH_KEY,
  CDP_PROFILE_MANAGE_KEY,
  CDP_PROFILE_UPLOAD_KEY,
  CDP_SEGMENTS_KEY,
  CDP_SOURCER_KEY,
  CDP_TRAFFIC_ANALYSIS_KEY,
  CDP_USER_JOURNEY_KEY
} from '../constant/cdp-constants';
import { CdpTenantUserConfig } from '../model/cdp-login';

@UntilDestroy()
@Injectable({
  providedIn: 'root'
})
export class CdpUserPermissionService {
  /**
   * When we receive the tenant details
   * we will get the configurations based on which the permission
   * & details of the user can be governed
   * @private
   */
  private userConfig: CdpTenantUserConfig | undefined;
  public featureConfig$ = this.userService.tenantDetailsUpdate$.
      pipe(
        map((details) => {
          if (details) {
            return details.config.isUserLevelCheckEnabled ? details.userLevelConfig : details.config.featureEnablingConfig
          }
          return undefined
        }),
    tap((c) => this.userConfig = c)
  );
  /**
   *
   * @param userService
   */
  constructor(private userService: CdpUserService) {
  }
  /**
   * Chk if user has permission for this
   * @param key
   */
  public hasPermission(featureKey: string) {
    switch (featureKey) {
      case CDP_HOME_KEY:
      case CDP_FAV_KEY:
      case CDP_DASHBOARD_KEY:
      case CDP_CAMPAIGN_KEY:
      case CDP_DATA_PIPELINE_KEY:
      case CDP_ANALYTICS_KEY:
      case CDP_EVENTS_DIRECTORY_KEY:
      case CDP_CUSTOMER_PROPS_KEY:
      case CDP_PROFILE_MANAGE_KEY:
      case CDP_CAMPAIGN_SUMMARY_KEY:
      case CDP_EVENTS_DASHBOARD_KEY:
      case CDP_OCCURRENCE_KEY:
      case CDP_EVENTS_KEY:
        return true;
      case CDP_TRAFFIC_ANALYSIS_KEY:
        return this.userConfig?.isURLReportingEnabled;
      case CDP_COHORT_KEY:
        return this.userConfig?.isCohortReportsEnabled;
      case CDP_FUNNEL_KEY:
        return this.userConfig?.isFunnelReportsEnabled;
      case CDP_PATH_KEY:
        return this.userConfig?.isPathMetricsReportsEnabled;
      case CDP_DESTINATIONS_KEY:
        return this.userConfig?.isDestinationEnabled;
      case CDP_PROFILE_UPLOAD_KEY:
        return this.userConfig?.isProfileUploadEnabled;
      case CDP_OFFLINE_DS_KEY:
        return this.userConfig?.isOfflineDataSourceEnabled;
      case CDP_SOURCER_KEY:
        return this.userConfig?.isSourceConnectionEnabled;
      case CDP_COV_KEY:
        return this.userConfig?.isCustomerOneViewEnabled;
      case CDP_SEGMENTS_KEY:
        return this.userConfig?.isSegmentsAvailable;
      case CDP_CHANNEL_KEY:
        return this.userConfig?.isChannelsAvailable;
      case CDP_USER_JOURNEY_KEY:
        return this.userConfig?.isFunnelReportsEnabled &&
          this.userConfig?.isPathMetricsReportsEnabled;
      default:
        return false;
    }
  }
}
