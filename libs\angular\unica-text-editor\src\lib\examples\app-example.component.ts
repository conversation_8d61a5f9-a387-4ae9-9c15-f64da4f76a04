import { Component } from '@angular/core';

/**
 * Example component showing how to pass suggestions from parent to text-block to unica-text-editor
 */
@Component({
  selector: 'app-example',
  template: `
    <div class="app-container">
      <h1>Text Editor with Dynamic Suggestions Example</h1>
      
      <!-- Example 1: Using text-block component with different suggestion sets -->
      <div class="example-section">
        <h2>Example 1: Text Block with Multiple Suggestion Sets</h2>
        <app-text-block
          [content]="content1"
          [showControls]="true"
          [showSuggestionList]="true"
          [initialSuggestionSet]="'user-profile'"
          (contentChange)="onContent1Change($event)"
          (tagSelected)="onTag1Selected($event)"
        ></app-text-block>
      </div>
      
      <!-- Example 2: Direct unica-text-editor with custom suggestions -->
      <div class="example-section">
        <h2>Example 2: Direct Text Editor with Custom Suggestions</h2>
        <unica-text-editor
          [content]="content2"
          [hashtagSuggestions]="customSuggestions"
          [placeholder]="'Type here and use # or click Tags button...'"
          (contentChange)="onContent2Change($event)"
          (hashtagSelected)="onTag2Selected($event)"
        ></unica-text-editor>
        
        <!-- Controls to change suggestions dynamically -->
        <div class="dynamic-controls">
          <h4>Change Suggestions Dynamically:</h4>
          <button (click)="loadEmailSuggestions()">Email Template Tags</button>
          <button (click)="loadInvoiceSuggestions()">Invoice Tags</button>
          <button (click)="loadReportSuggestions()">Report Tags</button>
          <button (click)="loadDefaultSuggestions()">Default Tags</button>
        </div>
        
        <div class="current-tags">
          <strong>Current Tags ({{ customSuggestions.length }}):</strong>
          <span *ngFor="let tag of customSuggestions; let last = last">
            {{ tag.display }}<span *ngIf="!last">, </span>
          </span>
        </div>
      </div>
      
      <!-- Example 3: Multiple editors with different suggestions -->
      <div class="example-section">
        <h2>Example 3: Multiple Editors with Different Suggestions</h2>
        
        <div class="multi-editor-container">
          <div class="editor-column">
            <h4>Email Editor</h4>
            <unica-text-editor
              [content]="emailContent"
              [hashtagSuggestions]="emailSuggestions"
              [placeholder]="'Email template...'"
              (contentChange)="onEmailContentChange($event)"
              (hashtagSelected)="onEmailTagSelected($event)"
            ></unica-text-editor>
          </div>
          
          <div class="editor-column">
            <h4>Report Editor</h4>
            <unica-text-editor
              [content]="reportContent"
              [hashtagSuggestions]="reportSuggestions"
              [placeholder]="'Report template...'"
              (contentChange)="onReportContentChange($event)"
              (hashtagSelected)="onReportTagSelected($event)"
            ></unica-text-editor>
          </div>
        </div>
      </div>
      
      <!-- Debug output -->
      <div class="debug-section">
        <h3>Debug Output</h3>
        <div class="debug-content">
          <div><strong>Content 1:</strong> {{ content1 || 'Empty' }}</div>
          <div><strong>Content 2:</strong> {{ content2 || 'Empty' }}</div>
          <div><strong>Email Content:</strong> {{ emailContent || 'Empty' }}</div>
          <div><strong>Report Content:</strong> {{ reportContent || 'Empty' }}</div>
          <div><strong>Last Selected Tag:</strong> {{ lastSelectedTag | json }}</div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .app-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .example-section {
      margin: 30px 0;
      padding: 20px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      background: #fafafa;
    }
    
    .dynamic-controls {
      margin: 15px 0;
      padding: 15px;
      background: #f0f0f0;
      border-radius: 6px;
    }
    
    .dynamic-controls button {
      margin: 5px;
      padding: 8px 16px;
      border: 1px solid #ccc;
      background: white;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .dynamic-controls button:hover {
      background: #e9e9e9;
    }
    
    .current-tags {
      margin: 10px 0;
      padding: 10px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .multi-editor-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 15px;
    }
    
    .editor-column {
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      background: white;
    }
    
    .debug-section {
      margin-top: 30px;
      padding: 15px;
      background: #f5f5f5;
      border-radius: 6px;
      font-family: monospace;
      font-size: 12px;
    }
    
    .debug-content div {
      margin: 5px 0;
      padding: 5px;
      background: white;
      border-radius: 3px;
    }
  `]
})
export class AppExampleComponent {
  // Content for different editors
  content1: string = '';
  content2: string = '';
  emailContent: string = '';
  reportContent: string = '';
  
  // Debug
  lastSelectedTag: any = null;
  
  // Custom suggestions for dynamic example
  customSuggestions = this.getDefaultSuggestions();
  
  // Predefined suggestions for multiple editors
  emailSuggestions = this.getEmailSuggestions();
  reportSuggestions = this.getReportSuggestions();

  // Event handlers
  onContent1Change(event: any) {
    this.content1 = event.html;
    console.log('Content 1 changed:', event);
  }

  onContent2Change(event: any) {
    this.content2 = event.html;
    console.log('Content 2 changed:', event);
  }

  onEmailContentChange(event: any) {
    this.emailContent = event.html;
    console.log('Email content changed:', event);
  }

  onReportContentChange(event: any) {
    this.reportContent = event.html;
    console.log('Report content changed:', event);
  }

  onTag1Selected(tag: any) {
    this.lastSelectedTag = { source: 'text-block', tag };
    console.log('Tag selected from text-block:', tag);
  }

  onTag2Selected(tag: any) {
    this.lastSelectedTag = { source: 'direct-editor', tag };
    console.log('Tag selected from direct editor:', tag);
  }

  onEmailTagSelected(tag: any) {
    this.lastSelectedTag = { source: 'email-editor', tag };
    console.log('Tag selected from email editor:', tag);
  }

  onReportTagSelected(tag: any) {
    this.lastSelectedTag = { source: 'report-editor', tag };
    console.log('Tag selected from report editor:', tag);
  }

  // Dynamic suggestion loading
  loadEmailSuggestions() {
    this.customSuggestions = this.getEmailSuggestions();
    console.log('Loaded email suggestions:', this.customSuggestions);
  }

  loadInvoiceSuggestions() {
    this.customSuggestions = this.getInvoiceSuggestions();
    console.log('Loaded invoice suggestions:', this.customSuggestions);
  }

  loadReportSuggestions() {
    this.customSuggestions = this.getReportSuggestions();
    console.log('Loaded report suggestions:', this.customSuggestions);
  }

  loadDefaultSuggestions() {
    this.customSuggestions = this.getDefaultSuggestions();
    console.log('Loaded default suggestions:', this.customSuggestions);
  }

  // Suggestion sets
  private getDefaultSuggestions() {
    return [
      { id: 'user-name', display: 'User Name' },
      { id: 'user-email', display: 'User Email' },
      { id: 'current-date', display: 'Current Date' },
      { id: 'current-time', display: 'Current Time' },
      { id: 'company-name', display: 'Company Name' }
    ];
  }

  private getEmailSuggestions() {
    return [
      { id: 'recipient-name', display: 'Recipient Name' },
      { id: 'recipient-email', display: 'Recipient Email' },
      { id: 'sender-name', display: 'Sender Name' },
      { id: 'sender-email', display: 'Sender Email' },
      { id: 'email-subject', display: 'Email Subject' },
      { id: 'email-date', display: 'Email Date' },
      { id: 'unsubscribe-link', display: 'Unsubscribe Link' },
      { id: 'company-logo', display: 'Company Logo' },
      { id: 'support-email', display: 'Support Email' },
      { id: 'website-url', display: 'Website URL' }
    ];
  }

  private getInvoiceSuggestions() {
    return [
      { id: 'invoice-number', display: 'Invoice Number' },
      { id: 'invoice-date', display: 'Invoice Date' },
      { id: 'due-date', display: 'Due Date' },
      { id: 'customer-name', display: 'Customer Name' },
      { id: 'customer-address', display: 'Customer Address' },
      { id: 'total-amount', display: 'Total Amount' },
      { id: 'tax-amount', display: 'Tax Amount' },
      { id: 'subtotal', display: 'Subtotal' },
      { id: 'payment-terms', display: 'Payment Terms' },
      { id: 'billing-address', display: 'Billing Address' }
    ];
  }

  private getReportSuggestions() {
    return [
      { id: 'report-title', display: 'Report Title' },
      { id: 'report-date', display: 'Report Date' },
      { id: 'report-author', display: 'Report Author' },
      { id: 'report-period', display: 'Report Period' },
      { id: 'total-records', display: 'Total Records' },
      { id: 'generated-time', display: 'Generated Time' },
      { id: 'data-source', display: 'Data Source' },
      { id: 'filter-criteria', display: 'Filter Criteria' },
      { id: 'page-number', display: 'Page Number' },
      { id: 'total-pages', display: 'Total Pages' }
    ];
  }
}
