import { Route } from '@angular/router';
import DashboardComponent from './components/dashboard/dashboard.component';
import EventsComponent from './components/events/events.component';

/**
 * The routes used by the application
 */
export const DetectAppRoutes: Route[] = [
    {
        path: 'home',
        component: DashboardComponent,
        data: {
            label: 'Home',
            icon: 'apps',
            id: 'home'
        },
    },
    {
        path: 'event',
        component: EventsComponent,
        data: {
            label: 'Events',
            icon: 'widgets',
            id: 'event'
        }
    },
    { path: '', redirectTo: 'home', pathMatch: 'full' }
]
