.side-nav-container {
  height: 100%;

  .nav-container {
    width: 72px;
    height: calc(100% - 32px);
    padding: 13px 0px 19px 0px;
    flex-direction: column;
    align-items: center;
    gap: 42px;
    background-color: #CDF4F6;
    .nav-element {
      padding: 22px;
      cursor: pointer;
      background-color: transparent;
      -webkit-transition: background-color 1s ease-out;
      -moz-transition: background-color 1s ease-out;
      -o-transition: background-color 1s ease-out;
      transition: background-color 1s ease-out;
      &:hover {
        background-color: #9ed6d7;
      }
    }
  }
}

.marketing-central-sub-menu {
  padding: 15px;
  .central-title {
    color: #fff;
    font-size: 18px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
  }
  .central-menus {
    display: table;
    clear: both;
    padding: 5px 10px 10px 5px;
    .menu-element {
      float: left;
      width: 50%;
      cursor: pointer;
      text-decoration: none;
      overflow: hidden;
      align-items: center;
      color: #fff;
      padding: 10px;
      display: flex;
      .text-container {
        width: calc(100% - 50px);
        white-space: nowrap;
        overflow: hidden;
        margin-left: 8px;
        margin-top: -3px;
      }

      &:hover {
        background-color: rgba(3, 141, 153, 0.3);
        transition: background-color 500ms;
      }
    }
  }
}
