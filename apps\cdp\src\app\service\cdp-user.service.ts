import { Injectable } from '@angular/core';
import {
  BehaviorSubject, catchError,
  combineLatest,
  distinctUntilChanged, forkJoin,
  map,
  Observable, of, ReplaySubject,
  skip,
  switchMap, tap
} from 'rxjs';
import { ApplicationToken, UnicaLogin, UserConfig } from '@hcl/unica-common';
import { StorageService, UserService } from '@hcl/angular/unica-angular-common';
import { TranslateService } from '@ngx-translate/core';
import { UnicaSnackBarService } from '@hcl/angular/unica-snack-bar';
import { CdpTenantDetails } from '../model/cdp-login';
import { CdpUserApiService } from './api/cdp-user-api.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

/**
 * CDP user service
 */
@UntilDestroy()
@Injectable({
  providedIn: 'root'
})
export class CdpUserService extends UserService {
  // the default name of the cookie that needs to be there
  private static TOKEN_COOKIE_NAME = '_lemapp';
  // this object holds the tenant details
  private tenantDetails: CdpTenantDetails | undefined = undefined;
  private tenantDetailsUpdateSubject: ReplaySubject<CdpTenantDetails | undefined> =
              new ReplaySubject<CdpTenantDetails | undefined>(1);
  public tenantDetailsUpdate$ = this.tenantDetailsUpdateSubject.asObservable()
              .pipe(
                tap((t)=> this.tenantDetails = t)
              );
  /**
   * @param userApiService
   * @param storage
   * @param translate
   * @param snackBarService
   */
  constructor(private storage: StorageService,
              private translate: TranslateService,
              private snackBarService: UnicaSnackBarService,
              private userApiService: CdpUserApiService) {
    super();
    // when the token is updated we have to write it to the cookie, so do that
    this.tokenUpdate$.pipe(untilDestroyed(this)).subscribe((t) => {
      // soave it to the cookie
      const token = this.getTokenDetails();
      if (token) {
        this.storage.setCookie(CdpUserService.TOKEN_COOKIE_NAME,
          token.accessToken);
      }
    });
    // subscribe for tenet changes as well, but do nothing
    this.tenantDetailsUpdate$.pipe(untilDestroyed(this)).subscribe();
  }
  /**
   * Get the user based on the session cookie
   */
  public override whoAmI(): Observable<UserConfig | undefined> {
    // get the cookie details from storage
    const cookieToken = this.storage.getCookie(CdpUserService.TOKEN_COOKIE_NAME);
    if (cookieToken && cookieToken != "") {
      // cdp the token in cookie is plain string
      this.tokenUpdateSubject.next({
        accessToken: cookieToken
      });
      return this.loadTenantAndUserDetails().pipe(
        catchError(() => {
          this.userDetailsUpdateSubject.next(undefined);
          this.storage.deleteCookie(CdpUserService.TOKEN_COOKIE_NAME);
          throw Error('Token Expired');
        })
      );
    } else {
      this.userDetailsUpdateSubject.next(undefined);
      this.storage.deleteCookie(CdpUserService.TOKEN_COOKIE_NAME);
    }
    return of(undefined);
  }
  /**
   * Try to login the user into the system
   * @param loginDetails
   */
  public override login(loginDetails: UnicaLogin): Observable<UserConfig> {
    // login has multiple steps associated to it
    // 1. do the login & get the token
    return this.userApiService.login(loginDetails)
      .pipe(
        switchMap((tokenDetails) => {
          // 2. if the token is proper we have set it
          this.tokenUpdateSubject.next(tokenDetails);
          // now we have to get the tenant details as we are login & also get the user details
          return this.loadTenantAndUserDetails();
        })
      );
  }
  /**
   * This will fetc the user & tenant details both from the server
   * @private
   */
  private loadTenantAndUserDetails(): Observable<UserConfig> {
    return forkJoin([
      this.userApiService.getUserTenant(),
      this.userApiService.getUserDetails()
    ]).pipe(map(([userTenant, userDetails]) => {
      this.tenantDetailsUpdateSubject.next(userTenant);
      // tell the service that the use has been changed
      this.userDetailsUpdateSubject.next(userDetails);
      return userDetails;
    }));
  }
  /**
   * Get the tenant details
   */
  public getTenantDetails(): CdpTenantDetails | undefined {
    return this.tenantDetails;
  }
}
