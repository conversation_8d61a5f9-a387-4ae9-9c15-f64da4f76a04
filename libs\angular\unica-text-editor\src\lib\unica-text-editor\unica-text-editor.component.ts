import TextAlignQuillModule from './../extentions/text-align.config';
import { TextAutocompleteDirective } from '../extentions';
import {
  HashtagPopupService,
  SuggestionItem,
  PopupPosition,
  PopupCallbacks
} from '../extentions/hashtag-popup.service';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  input,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ContentChange,
  QuillEditorComponent,
  QuillModule,
  QuillToolbarConfig,
} from 'ngx-quill';
import Quill from 'quill';
import { EditorTextNode } from '../extentions/text-node';
// Register the custom blots for Quill 2.0
try {
  Quill.register('formats/editortextblock', EditorTextNode, true);
} catch (error) {
  console.warn('Blot registration warning:', error);
}
import { FormsModule } from '@angular/forms';
import { computeEditorStyles } from '../utils/style.utils';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TextEditorStyles } from '../editor.model';
import { GenerateUniqueId } from '@hcl/unica-common';

@Component({
  selector: 'unica-text-editor',
  standalone: true,
  imports: [
    CommonModule,
    QuillModule,
    FormsModule,
    TranslateModule,
    TextAutocompleteDirective,
  ],
  templateUrl: './unica-text-editor.component.html',
  styleUrls: [
    './unica-text-editor.component.scss',
    '../extentions/hashtag-autocomplete.styles.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None, // Required for Quill styles to work properly
  host: {
    class: 'unica-text-editor-host',
  },
})
export class UnicaTextEditorComponent implements OnInit {
  translate = inject(TranslateService);
  private popupService = inject(HashtagPopupService);
  /**
   * Reference to the Quill editor component
   */ @ViewChild(QuillEditorComponent, { static: false })
  quillEditorComponent!: QuillEditorComponent;
  // No need for direct Quill reference or element reference to the editor

  /**
   * The content of the editor
   */
  // @Input() content!: string;

  html!: string;
  @Input()
  set content(b: string | null | undefined) {
    // Handle null/undefined values by clearing the editor
    if (b === null || b === undefined) {
      this.html = '';
      return;
    }

    // Handle empty string by clearing the editor
    if (b === '') {
      this.html = '';
      return;
    }

    // Only update html if content is different from current html
    if (b !== this.html) {
      this.html = this.parseHTMLForHyperlinks(b);
    }
  }

  get content(): string {
    return this.html || '';
  }

  /**
   * Event emitter for content changes
   */
  @Output() contentChange = new EventEmitter<ContentChange>();

  /**
   * Whether the editor is read-only
   */
  readOnly = input(false);

  /**
   * Placeholder for the editor
   */
  placeholder = input(this.translate.instant('TEXT_EDITOR.PLACEHOLDER'));

  /**
   * Styles for the text editor
   */
  styles = input<TextEditorStyles | undefined>(undefined);

  required = input(true);

  /**
   * Hashtag suggestions for the autocomplete dropdown
   */
  @Input() hashtagSuggestions: { id: string; display: string }[] = [];

  /**
   * Event emitted when a hashtag suggestion is selected
   */
  @Output() hashtagSelected = new EventEmitter<{
    id: string;
    display: string;
  }>();

  ngOnInit() {
    TextAlignQuillModule();

    // Initialize popup service
    this.popupService.initialize();
  }

  /**
   * Get the available formats for the Quill editor
   */
  getQuillFormats(): string[] {
    return [
      'bold',
      'italic',
      'underline',
      'strike',
      'blockquote',
      'code-block',
      'header',
      'list',
      'script',
      'indent',
      'direction',
      'size',
      'color',
      'background',
      'font',
      'align',
      'link',
      'editortextblock',
    ];
  }

  parseHTMLForHyperlinks(b: string) {
    const domObject = new DOMParser().parseFromString(b, 'text/html');
    const allLinks = domObject.querySelectorAll('a');
    allLinks.forEach((elem) => {
      if (!elem.getAttribute('data-id')) {
        elem.setAttribute('data-id', GenerateUniqueId());
      }
    });
    b = domObject.body.innerHTML;

    return b;
  }

  getQuillConfig() {
    const container: QuillToolbarConfig = [
      ['bold', 'italic', 'underline', 'strike'],
      [
        { align: ['', 'center', 'right', 'justify'] },
        {
          color: [
            '#000000',
            '#e60000',
            '#ff9900',
            '#ffff00',
            '#008a00',
            '#0066cc',
            '#9933ff',
            '#ffffff',
            '#facccc',
            '#ffebcc',
            '#ffffcc',
            '#cce8cc',
            '#cce0f5',
            '#ebd6ff',
            '#bbbbbb',
            '#f06666',
            '#ffc266',
            '#ffff66',
            '#66b966',
            '#66a3e0',
            '#c285ff',
            '#888888',
            '#a10000',
            '#b26b00',
            '#b2b200',
            '#006100',
            '#0047b2',
            '#6b24b2',
            '#444444',
            '#5c0000',
            '#663d00',
            '#666600',
            '#003700',
            '#002966',
            '#3d1466',
            'custom-color',
          ],
        },
        'link',
        { tags: ['📝'] }, // Using ql-option approach - Tags button
        'rule',
      ],
      ['clean'],
    ];
    return {
      toolbar: {
        container,
        handlers: {
          tags: this.tagsHandler.bind(this),
        },
      },
    };
  }

  /**
   * Handler for placeholder button in toolbar
   */
  placeholderHandler(): void {
    // Placeholder handler implementation
  }

  /**
   * Handler for hyperlink button in toolbar
   */
  hyperLinkHandler(): void {
    // Hyperlink handler implementation
  }

  /**
   * Handler for rule button in toolbar
   */
  ruleHandler(): void {
    // Rule handler implementation
  }

  /**
   * Handler for tags button in toolbar
   * @param _value - The selected option value (for ql-option approach)
   */
  tagsHandler(_value?: string): void {
    if (!this.editorInstance) {
      return;
    }

    // Get current cursor position
    const selection = this.editorInstance.getSelection();
    if (!selection) {
      return;
    }

    // Get cursor bounds for popup positioning
    const bounds = this.editorInstance.getBounds(selection.index);
    const editorContainer = this.editorInstance.container;

    if (!bounds || !editorContainer) {
      return;
    }

    const rect = editorContainer.getBoundingClientRect();

    // Create position for popup
    const position: PopupPosition = {
      top: rect.top + bounds.top,
      left: rect.left + bounds.left,
      height: bounds.height,
    };

    // Create callbacks for popup interaction
    const callbacks: PopupCallbacks = {
      onItemSelected: (item: SuggestionItem) => {
        this.insertSuggestionAtCursor(item);
      },
      onMenuClosed: () => {
        // Focus back to editor after popup closes
        this.editorInstance?.focus();
      },
    };

    // Get all available suggestions
    const suggestions = this.getDefaultHashtagSuggestions();

    // Open popup menu
    this.popupService.openMenu(suggestions, position, callbacks);
  }

  /**
   * Insert selected suggestion at current cursor position
   */
  private insertSuggestionAtCursor(item: SuggestionItem): void {
    if (!this.editorInstance) return;

    const selection = this.editorInstance.getSelection();
    if (!selection) return;

    const newLabel = `<--${item.display}-->`;

    try {
      // Create delta for insertion
      const delta = [
        { retain: selection.index },
        {
          insert: {
            editortextblock: {
              label: newLabel,
              id: item.id,
              length: newLabel.length,
            },
          },
        },
        { insert: ' ' },
      ];

      // Apply the delta
      this.editorInstance.updateContents(delta, 'user');

      // Set cursor position after the inserted text block and space
      this.editorInstance.setSelection(selection.index + 2, 0);

      // Emit the hashtag selected event
      this.hashtagSelected.emit(item);

    } catch (error) {
      console.error('Error inserting suggestion:', error);
    }
  }

  /**
   * Handler for AI button in toolbar
   */
  aiHandler(): void {
    // AI handler implementation
  }

  /**
   * Handler for content changes
   * @param event - The content change event from the editor
   */
  onContentChanged(event: ContentChange): void {
    // Update the content and emit the changes
    // Handle null/undefined html by setting to empty string
    this.html = event.html ?? '';

    // Create a modified event with proper null handling
    const modifiedEvent: ContentChange = {
      ...event,
      html: this.html,
      text: event.text ?? '',
    };
    console.log('Content changed:', modifiedEvent);
    this.contentChange.emit(modifiedEvent);
  }
  public editorInstance!: Quill;
  /**
   * Handler for editor creation
   * @param editor - The Quill editor instance
   */
  onEditorCreated(editor: Quill): void {
    this.editorInstance = editor;

    // If html is empty, ensure the editor is also empty
    if (!this.html || this.html === '') {
      editor.setText('');
    }

    // Toolbar handlers are now registered in getQuillConfig()

    // Editor created handling
    // Editor is now ready for use
  }



  /**
   * Get computed styles for the editor based on the styles input
   * Uses utility function for better maintainability
   */
  getComputedStyles(): Record<string, string> {
    return computeEditorStyles(this.styles());
  }

  /**
   * Handle a selected hashtag from the autocomplete directive
   * @param suggestion - The selected hashtag suggestion
   */
  onHashtagSelected(suggestion: { id: string; display: string }): void {
    this.hashtagSelected.emit(suggestion);
  }

  /**
   * Get default hashtag suggestions if none are provided
   */
  getDefaultHashtagSuggestions(): { id: string; display: string }[] {
    if (this.hashtagSuggestions.length === 0) {
      return [
        // User Information Tags
        { id: 'user-name', display: 'User Name' },
        { id: 'user-email', display: 'User Email' },
        { id: 'user-phone', display: 'User Phone' },
        { id: 'user-company', display: 'User Company' },
        { id: 'user-title', display: 'User Title' },

        // Date & Time Tags
        { id: 'current-date', display: 'Current Date' },
        { id: 'current-time', display: 'Current Time' },
        { id: 'due-date', display: 'Due Date' },
        { id: 'created-date', display: 'Created Date' },

        // Document Tags
        { id: 'document-title', display: 'Document Title' },
        { id: 'document-id', display: 'Document ID' },
        { id: 'document-version', display: 'Document Version' },

        // System Tags
        { id: 'system-name', display: 'System Name' },
        { id: 'environment', display: 'Environment' },
        { id: 'application-url', display: 'Application URL' },

        // Custom Tags
        { id: 'project-name', display: 'Project Name' },
        { id: 'department', display: 'Department' },
        { id: 'location', display: 'Location' },
        { id: 'reference-number', display: 'Reference Number' },
        { id: 'status', display: 'Status' },
      ];
    }
    return this.hashtagSuggestions;
  }

  /**
   * This function will return a string after removing the enter key based on browser.
   */
  browserCheck(): string {
    if (!this.quillEditorComponent?.quillEditor) {
      return '';
    }

    const innerText =
      this.quillEditorComponent.quillEditor.keyboard.quill.editor.scroll.domNode
        .innerText;
    const userAgentString = navigator.userAgent;
    const IExplorerAgent =
      userAgentString.indexOf('MSIE') > -1 ||
      userAgentString.indexOf('rv:') > -1;
    let textString = '';
    if (IExplorerAgent) {
      textString = innerText.split('\r\n\r\n').join(' ');
    } else {
      textString = innerText.split('\n\n').join(' ');
    }
    return textString;
  }

  /**
   * Programmatically clear the editor content
   */
  clearContent(): void {
    this.html = '';
    if (this.editorInstance) {
      this.editorInstance.setText('');
    }
  }

  /**
   * Get the current text content without HTML formatting
   */
  getTextContent(): string {
    if (this.editorInstance) {
      return this.editorInstance.getText();
    }
    return '';
  }

  /**
   * Check if the editor is empty
   */
  isEmpty(): boolean {
    if (this.editorInstance) {
      return this.editorInstance.getLength() <= 1; // Quill always has at least one character (newline)
    }
    return !this.html || this.html.trim() === '';
  }

  /**
   * Test method to manually insert a hashtag (for debugging)
   */
  testInsertHashtag(label = 'Test Hashtag', id = 'test-id'): void {
    if (!this.editorInstance) {
      console.error('No editor instance available');
      return;
    }

    try {
      const selection = this.editorInstance.getSelection();
      const index = selection
        ? selection.index
        : this.editorInstance.getLength() - 1;

      console.log('Test inserting at index:', index);

      // First try simple text insertion
      const testText = `@${label} `;
      this.editorInstance.insertText(index, testText, 'user');
      this.editorInstance.setSelection(index + testText.length, 0);

      console.log('Simple text inserted successfully:', testText);

      // TODO: Test blot insertion separately
      /*
      const delta = [
        { retain: index },
        {
          insert: {
            editortextblock: {
              label: label,
              id: id,
              length: label.length,
            },
          },
        },
        { insert: ' ' },
      ];

      console.log('Test inserting hashtag at index:', index, 'with delta:', delta);
      this.editorInstance.updateContents(delta, 'user');
      this.editorInstance.setSelection(index + 2, 0);
      console.log('Test hashtag inserted successfully');
      */
    } catch (error) {
      console.error('Error in test hashtag insertion:', error);
    }
  }

  /**
   * Test blot insertion separately
   */
  testBlotInsertion(label = 'Test Blot', id = 'test-blot-id'): void {
    if (!this.editorInstance) {
      console.error('No editor instance available');
      return;
    }

    try {
      const selection = this.editorInstance.getSelection();
      const index = selection
        ? selection.index
        : this.editorInstance.getLength() - 1;

      console.log('Testing blot insertion at index:', index);
      console.log('Available formats:', this.editorInstance.getFormat());

      const delta = [
        { retain: index },
        {
          insert: {
            editortextblock: {
              label: label,
              id: id,
              length: label.length,
            },
          },
        },
        { insert: ' ' },
      ];

      console.log('Test inserting blot with delta:', delta);
      this.editorInstance.updateContents(delta, 'user');
      this.editorInstance.setSelection(index + 2, 0);
      console.log('Test blot inserted successfully');

      // Check if the blot was actually inserted
      setTimeout(() => {
        const content = this.editorInstance.getContents();
        console.log('Editor contents after blot insertion:', content);

        const htmlContent = this.editorInstance.root.innerHTML;
        console.log('HTML content:', htmlContent);
      }, 100);
    } catch (error) {
      console.error('Error in test blot insertion:', error);
      console.error('Error stack:', (error as Error).stack);
    }
  }

  /**
   * Test ONLY blot insertion to isolate the issue
   */
  testOnlyBlot(label = 'BLOT TEST', id = 'blot-test-id'): void {
    if (!this.editorInstance) {
      console.error('No editor instance available');
      return;
    }

    try {
      console.log('=== TESTING BLOT INSERTION ===');

      const selection = this.editorInstance.getSelection();
      const index = selection ? selection.index : 0;

      console.log('Current index:', index);
      console.log('Editor length:', this.editorInstance.getLength());

      // Test if the blot is registered
      const blotClass = Quill.import('formats/editortextblock');
      console.log('Blot class:', blotClass);

      const blotData = {
        label: label,
        id: id,
        length: label.length,
      };

      console.log('Blot data:', blotData);

      const delta = [
        { retain: index },
        {
          insert: {
            editortextblock: blotData,
          },
        },
        { insert: ' ' },
      ];

      console.log('Delta to apply:', delta);

      this.editorInstance.updateContents(delta, 'user');

      console.log('Delta applied successfully');

      // Check the result
      setTimeout(() => {
        const contents = this.editorInstance.getContents();
        console.log('Editor contents after blot insertion:', contents);

        const html = this.editorInstance.root.innerHTML;
        console.log('HTML after blot insertion:', html);
      }, 100);
    } catch (error) {
      console.error('Error in blot insertion test:', error);
      console.error('Error stack:', (error as Error).stack);
    }
  }

  /**
   * Test blot creation directly
   */
  testBlotCreation(): void {
    try {
      console.log('=== TESTING BLOT CREATION DIRECTLY ===');

      // Test if we can create the blot directly
      const BlotClass = Quill.import('formats/editortextblock');
      console.log('Blot class imported:', BlotClass);

      if (BlotClass) {
        const testValue = {
          label: 'Direct Test',
          id: 'direct-test',
          length: 11,
        };

        console.log('Creating blot with value:', testValue);
        const node = (BlotClass as any).create(testValue);
        console.log('Created node:', node);
        console.log('Node HTML:', node.outerHTML);

        // Test if the node has the right properties
        console.log('Node class:', node.className);
        console.log('Node id:', node.id);
        console.log(
          'Node contenteditable:',
          node.getAttribute('contenteditable'),
        );
      } else {
        console.error('Blot class not found!');
      }
    } catch (error) {
      console.error('Error in direct blot creation:', error);
    }
  }

  /**
   * Test if blot registration is working
   */
  testBlotRegistration(): void {
    try {
      console.log('=== TESTING BLOT REGISTRATION ===');

      // Check all registered formats
      console.log('All Quill imports:', Object.keys(Quill.imports));

      // Check if our blot is registered
      const ourBlot = Quill.import('formats/editortextblock');
      console.log('Our blot registered:', !!ourBlot);
      console.log('Our blot class:', ourBlot);

      if (ourBlot) {
        console.log('Blot name:', (ourBlot as any).blotName);
        console.log('Blot tagName:', (ourBlot as any).tagName);
        console.log('Blot className:', (ourBlot as any).className);
      }
    } catch (error) {
      console.error('Error checking blot registration:', error);
    }
  }
}
