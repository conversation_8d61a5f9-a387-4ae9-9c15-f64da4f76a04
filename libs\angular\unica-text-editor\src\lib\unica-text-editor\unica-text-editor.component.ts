import TextAlignQuillModule from './../extentions/text-align.config';
import { TextAutocompleteDirective } from '../extentions';
import {
  QuillTagsService,
  TagSuggestion,
} from '../extentions/quill-tags.service';
import { createQuillTagsHelper } from '../extentions/quill-tags.helper';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  input,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ContentChange,
  QuillEditorComponent,
  QuillModule,
  QuillToolbarConfig,
} from 'ngx-quill';
import Quill from 'quill';
import { EditorTextNode } from '../extentions/text-node';
// Register the custom blots for Quill 2.0
try {
  Quill.register('formats/editortextblock', EditorTextNode, true);
} catch (error) {
  console.warn('Blot registration warning:', error);
}
import { FormsModule } from '@angular/forms';
import { computeEditorStyles } from '../utils/style.utils';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TextEditorStyles } from '../editor.model';
import { GenerateUniqueId } from '@hcl/unica-common';

@Component({
  selector: 'unica-text-editor',
  standalone: true,
  imports: [
    CommonModule,
    QuillModule,
    FormsModule,
    TranslateModule,
    TextAutocompleteDirective,
  ],
  templateUrl: './unica-text-editor.component.html',
  styleUrls: [
    './unica-text-editor.component.scss',
    '../extentions/hashtag-autocomplete.styles.scss',
  ],
  providers: [QuillTagsService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None, // Required for Quill styles to work properly
  host: {
    class: 'unica-text-editor-host',
  },
})
export class UnicaTextEditorComponent implements OnInit {
  translate = inject(TranslateService);
  private tagsService = inject(QuillTagsService);
  private tagsHelper = createQuillTagsHelper(this.tagsService);
  /**
   * Reference to the Quill editor component
   */ @ViewChild(QuillEditorComponent, { static: false })
  quillEditorComponent!: QuillEditorComponent;
  // No need for direct Quill reference or element reference to the editor

  /**
   * The content of the editor
   */
  // @Input() content!: string;

  html!: string;
  @Input()
  set content(b: string | null | undefined) {
    // Handle null/undefined values by clearing the editor
    if (b === null || b === undefined) {
      this.html = '';
      return;
    }

    // Handle empty string by clearing the editor
    if (b === '') {
      this.html = '';
      return;
    }

    // Only update html if content is different from current html
    if (b !== this.html) {
      this.html = this.parseHTMLForHyperlinks(b);
    }
  }

  get content(): string {
    return this.html || '';
  }

  /**
   * Event emitter for content changes
   */
  @Output() contentChange = new EventEmitter<ContentChange>();

  /**
   * Whether the editor is read-only
   */
  readOnly = input(false);

  /**
   * Placeholder for the editor
   */
  placeholder = input(this.translate.instant('TEXT_EDITOR.PLACEHOLDER'));

  /**
   * Styles for the text editor
   */
  styles = input<TextEditorStyles | undefined>(undefined);

  required = input(true);

  /**
   * Hashtag suggestions for the autocomplete dropdown
   */
  @Input()
  set suggestionList(suggestions: { id: string; display: string }[]) {
    this._suggestionList = suggestions || [];
    // Update Tags service when suggestions change
    if (this.tagsService) {
      this.tagsService.updateSuggestions(this._suggestionList);
    }
  }

  get suggestionList(): { id: string; display: string }[] {
    return this._suggestionList;
  }

  private _suggestionList: { id: string; display: string }[] = [];

  /**
   * Event emitted when a hashtag suggestion is selected
   */
  @Output() hashtagSelected = new EventEmitter<{
    id: string;
    display: string;
  }>();

  ngOnInit() {
    TextAlignQuillModule();

    // Initialize Tags service with passed suggestions or defaults
    this.initializeTagsService();
  }

  /**
   * Initialize Tags service with current suggestions
   */
  private initializeTagsService(): void {
    const suggestions = this.suggestionList;

    this.tagsHelper = createQuillTagsHelper(this.tagsService);
    this.tagsHelper.setup({
      suggestions: suggestions,
      onTagSelected: (tag: TagSuggestion) => {
        this.hashtagSelected.emit(tag);
      },
      insertFormat: 'textblock',
      addSpace: true,
    });
  }

  /**
   * Get the available formats for the Quill editor
   */
  getQuillFormats(): string[] {
    return [
      'bold',
      'italic',
      'underline',
      'strike',
      'blockquote',
      'code-block',
      'header',
      'list',
      'script',
      'indent',
      'direction',
      'size',
      'color',
      'background',
      'font',
      'align',
      'link',
      'editortextblock',
    ];
  }

  parseHTMLForHyperlinks(b: string) {
    const domObject = new DOMParser().parseFromString(b, 'text/html');
    const allLinks = domObject.querySelectorAll('a');
    allLinks.forEach((elem) => {
      if (!elem.getAttribute('data-id')) {
        elem.setAttribute('data-id', GenerateUniqueId());
      }
    });
    b = domObject.body.innerHTML;

    return b;
  }

  getQuillConfig() {
    // For bubble theme, we configure the toolbar that appears on text selection
    const container: QuillToolbarConfig = [
      ['bold', 'italic', 'underline'],
      [
        {
          color: [
            '#000000',
            '#e60000',
            '#ff9900',
            '#ffff00',
            '#008a00',
            '#0066cc',
            '#9933ff',
            '#ffffff',
            '#facccc',
            '#ffebcc',
            '#ffffcc',
            '#cce8cc',
            '#cce0f5',
            '#ebd6ff',
            '#bbbbbb',
            '#f06666',
            '#ffc266',
            '#ffff66',
            '#66b966',
            '#66a3e0',
            '#c285ff',
            '#888888',
            '#a10000',
            '#b26b00',
            '#b2b200',
            '#006100',
            '#0047b2',
            '#6b24b2',
            '#444444',
            '#5c0000',
            '#663d00',
            '#666600',
            '#003700',
            '#002966',
            '#3d1466',
            'custom-color',
          ],
        },
        'link',
      ],
    ];

    const suggestions = this.suggestionList;
    const tags = this.tagsHelper.setup({
      suggestions: suggestions,
      onTagSelected: (tag: TagSuggestion) => {
        this.hashtagSelected.emit(tag);
      },
      insertFormat: 'textblock',
      addSpace: true,
    });

    // Add tags to toolbar
    tags.addToToolbar(container);

    return {
      toolbar: {
        container,
        handlers: {
          ...tags.getHandlers(),
        },
      },
    };
  }

  /**
   * Handler for placeholder button in toolbar
   */
  placeholderHandler(): void {
    // Placeholder handler implementation
  }

  /**
   * Handler for hyperlink button in toolbar
   */
  hyperLinkHandler(): void {
    // Hyperlink handler implementation
  }

  /**
   * Handler for rule button in toolbar
   */
  ruleHandler(): void {
    // Rule handler implementation
  }

  /**
   * Handler for AI button in toolbar
   */
  aiHandler(): void {
    // AI handler implementation
  }

  /**
   * Handler for content changes
   * @param event - The content change event from the editor
   */
  onContentChanged(event: ContentChange): void {
    // Update the content and emit the changes
    // Handle null/undefined html by setting to empty string
    this.html = event.html ?? '';

    // Create a modified event with proper null handling
    const modifiedEvent: ContentChange = {
      ...event,
      html: this.html,
      text: event.text ?? '',
    };
    console.log('Content changed:', modifiedEvent);
    this.contentChange.emit(modifiedEvent);
  }
  public editorInstance!: Quill;
  /**
   * Handler for editor creation
   * @param editor - The Quill editor instance
   */
  onEditorCreated(editor: Quill): void {
    this.editorInstance = editor;

    // If html is empty, ensure the editor is also empty
    if (!this.html || this.html === '') {
      editor.setText('');
    }

    // Initialize Tags service with editor instance
    const suggestions = this.suggestionList;
    const tags = this.tagsHelper.setup({
      suggestions: suggestions,
      onTagSelected: (tag: TagSuggestion) => {
        this.hashtagSelected.emit(tag);
      },
      insertFormat: 'textblock',
      addSpace: true,
    });
    tags.onEditorCreated(editor);
  }

  /**
   * Get computed styles for the editor based on the styles input
   * Uses utility function for better maintainability
   */
  getComputedStyles(): Record<string, string> {
    return computeEditorStyles(this.styles());
  }

  /**
   * Handle a selected hashtag from the autocomplete directive
   * @param suggestion - The selected hashtag suggestion
   */
  onHashtagSelected(suggestion: { id: string; display: string }): void {
    this.hashtagSelected.emit(suggestion);
  }

}
