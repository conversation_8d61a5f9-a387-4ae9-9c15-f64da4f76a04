import {
  Directive,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import Quill from 'quill';
import { AfterViewInit } from '@angular/core';
import { EditorTextNode, TextNodeClickEvent } from './text-node';
import { Subscription } from 'rxjs';
import {
  HashtagPopupService,
  SuggestionItem,
  PopupPosition,
  PopupCallbacks,
} from './hashtag-popup.service';

// Import Parchment for blot operations
const Parchment = Quill.import('parchment');

@Directive({
  selector: '[unicaTextAutocomplete]',
  standalone: true,
})
export class TextAutocompleteDirective
  implements OnInit, OnDestroy, AfterViewInit, OnChanges
{
  @Input() suggestions: SuggestionItem[] = [];
  @Input() quillEditor?: unknown; // Will be passed from the template - will be cast to Quill later
  @Input() autoCompleteChar = '#';
  @Output() suggestionSelected = new EventEmitter<SuggestionItem>();

  private quillEditorRef: Quill | null = null;
  private editorInitialized = false;

  // State management
  private isAutoSuggestMode = false;

  // Character tracking
  private charAfterSpecialSymbol = '';
  private rangeToInsertText: { index: number; length: number } | null = null;

  // Navigation and suggestions
  private lastTypedChar = '';
  private originalSuggestions: SuggestionItem[] = [];

  // Edit mode for existing text blocks
  private isEditMode = false;
  private editingTextNode?: HTMLElement;

  // Subscriptions
  private textNodeClickSubscription?: Subscription;

  // Popup callbacks
  private popupCallbacks: PopupCallbacks = {
    onItemSelected: this.handleItemSelected.bind(this),
    onMenuClosed: this.handleMenuClosed.bind(this),
  };

  constructor(
    private popupService: HashtagPopupService,
  ) {
    // Initialize popup callbacks
    this.popupCallbacks = {
      onItemSelected: this.handleItemSelected.bind(this),
      onMenuClosed: this.handleMenuClosed.bind(this),
    };
  }

  ngOnInit(): void {
    // Initialize original suggestions
    this.originalSuggestions = [...this.suggestions];
  }

  ngOnChanges(changes: SimpleChanges): void {
    // React to changes in the quillEditor input
    if (changes['quillEditor'] && changes['quillEditor'].currentValue && !this.editorInitialized) {
      this.initializeEditor();
    }

    // Update suggestions if they change
    if (changes['suggestions'] && changes['suggestions'].currentValue) {
      this.originalSuggestions = [...this.suggestions];
    }
  }

  /**
   * Initialize the editor when it becomes available
   */
  private initializeEditor(): void {
    if (this.quillEditor && !this.editorInitialized) {
      this.quillEditorRef = this.quillEditor as unknown as Quill;

      // Check if the editor is actually ready (has a root element)
      if (this.quillEditorRef && this.quillEditorRef.root) {
        this.setupQuillListeners();
        this.editorInitialized = true;
      } else {
        // Editor not ready yet, try again after a short delay
        setTimeout(() => this.initializeEditor(), 100);
      }
    }
  }

  ngAfterViewInit(): void {
    // Initialize popup service
    this.popupService.initialize();

    // Subscribe to text node click events
    this.subscribeToTextNodeClicks();

    // Try to initialize if editor is already available (fallback)
    if (this.quillEditor && !this.editorInitialized) {
      this.initializeEditor();
    }
  }

  private setupQuillListeners(): void {
    if (this.quillEditorRef) {
      // Initialize editor event handlers
      this.initEditorEventHandlers();
    }
  }



  /**
   * Initialize editor event handlers for autocomplete detection
   */
  private initEditorEventHandlers(): void {
    if (!this.quillEditorRef) return;

    this.quillEditorRef.root.addEventListener(
      'keydown',
      this.keyDownEventHandler.bind(this),
    );
    this.quillEditorRef.root.addEventListener(
      'click',
      this.mouseDownEventHandler.bind(this),
    );
    this.quillEditorRef.root.addEventListener(
      'paste',
      this.pasteEventHandler.bind(this),
    );
  }

  ngOnDestroy(): void {
    // Destroy popup service
    this.popupService.destroy();

    // Unsubscribe from text node click events
    if (this.textNodeClickSubscription) {
      this.textNodeClickSubscription.unsubscribe();
    }

    // Remove event listeners from Quill editor
    if (this.quillEditorRef) {
      this.quillEditorRef.root.removeEventListener(
        'keydown',
        this.keyDownEventHandler.bind(this),
      );
      this.quillEditorRef.root.removeEventListener(
        'click',
        this.mouseDownEventHandler.bind(this),
      );
      this.quillEditorRef.root.removeEventListener(
        'paste',
        this.pasteEventHandler.bind(this),
      );
    }
  }

  /**
   * Handle item selection from popup
   */
  private handleItemSelected(item: SuggestionItem): void {
    if (this.isEditMode && this.editingTextNode) {
      // Edit mode: replace the existing text block
      this.replaceExistingTextBlock(item.id, item.display);
    } else {
      // Normal mode: insert new text block
      this.insertNewTextBlock(item.id, item.display);
    }

    // Reset edit mode
    this.isEditMode = false;
    this.editingTextNode = undefined;

    // Clean up after successful insertion
    this.postPopUpCloseCallback();

    // Emit selection event
    this.suggestionSelected.emit(item);
  }

  /**
   * Handle menu closed from popup
   */
  private handleMenuClosed(): void {
    // Clean up if menu was closed without item selection
    if (this.isAutoSuggestMode) {
      this.postPopUpCloseCallback();
    }
  }

  /**
   * Subscribe to text node click events from the text node blot
   */
  private subscribeToTextNodeClicks(): void {
    this.textNodeClickSubscription = EditorTextNode.TextNodeClick$.subscribe(
      (clickEvent: TextNodeClickEvent) => {
        if (clickEvent.action === 'edit' && clickEvent.node) {
          this.openDropdownForEdit(clickEvent);
        }
      },
    );
  }

  /**
   * Open dropdown for editing an existing text block
   */
  private openDropdownForEdit(clickEvent: TextNodeClickEvent): void {
    if (!this.quillEditorRef || !clickEvent.node) return;

    // Set edit mode
    this.isEditMode = true;
    this.editingTextNode = clickEvent.node;

    // Find the current item in suggestions and pre-select it
    const currentItem = this.originalSuggestions.find(
      (s) => s.id === clickEvent.value.id,
    );

    if (currentItem) {
      // Set the current suggestions to show all options
      this.suggestions = [...this.originalSuggestions];

      // Get position from the clicked element
      const rect = clickEvent.node.getBoundingClientRect();
      const position: PopupPosition = {
        top: rect.top,
        left: rect.left,
        height: rect.height,
      };

      // Open the popup menu
      this.popupService.openMenu(
        this.suggestions,
        position,
        this.popupCallbacks,
      );

      // After menu is created, scroll to and highlight the current selection
      setTimeout(() => {
        this.popupService.scrollToAndSelectItem(currentItem);
      }, 50);
    }
  }

  /**
   * Handle paste events to reset autocomplete detection
   */
  private pasteEventHandler(_evt: Event): void {
    setTimeout(() => {
      this.resetLastTypedChar();
    }, 200);
  }

  /**
   * Handle mouse down events to detect text block element clicks
   */
  private mouseDownEventHandler(event: MouseEvent): void {
    const clickedElement = event.target as HTMLElement;
    if (
      clickedElement.classList.contains('droppable') &&
      !clickedElement.classList.contains('link-droppable')
    ) {
      this.textBlockElementClicked(event);
    }
  }

  /**
   * Handle key down events for autocomplete detection and navigation
   */
  private keyDownEventHandler(event: KeyboardEvent): void {
    const e = event;

    // Detect autocomplete trigger
    if (
      ((e.shiftKey && e.key === this.autoCompleteChar) || e.key === this.autoCompleteChar) &&
      this.lastTypedChar !== this.autoCompleteChar
    ) {
      this.insertAutoSuggestNode();
      return; // Exit early after triggering autocomplete
    }

    // Only handle navigation keys if popup is open
    if (this.popupService && this.popupService.isOpen()) {
      switch (e.code) {
        case 'Escape':
          this.popupService.closeMenu();
          event.preventDefault();
          event.stopPropagation();
          return;
        case 'ArrowUp':
          this.popupService.navigatePrevious();
          event.preventDefault();
          event.stopPropagation();
          return;
        case 'ArrowDown':
          this.popupService.navigateNext();
          event.preventDefault();
          event.stopPropagation();
          return;
        case 'Enter':
          // Prevent default behavior first
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();

          // Then select the item
          this.popupService.selectCurrentItem();
          return;
      }
    } else {
      // Handle other keys when popup is not open
      switch (e.code) {
        case 'Delete':
        case 'Backspace':
          // Only call deleteTextBlockFields if we're not in auto-suggest mode
          // In auto-suggest mode, backspace is handled by getTypedChar
          if (!this.isAutoSuggestMode) {
            this.deleteTextBlockFields();
          }
          break;
        case 'Space':
        case 'Tab':
          this.resetLastTypedChar();
          break;
      }
    }
  }

  /**
   * Handle text block element clicks
   */
  private textBlockElementClicked(event: MouseEvent): void {
    if (!this.quillEditorRef) return;

    const target = event.target as HTMLElement;

    // Handle Safari specific behavior
    const isSafari =
      /Safari/.test(navigator.userAgent) &&
      /Apple Computer/.test(navigator.vendor);
    if (isSafari) {
      target.classList.add('read-write');
    }

    try {
      const blot = (Parchment as any).find(target);
      if (blot) {
        const index = this.quillEditorRef.getIndex(blot);
        this.quillEditorRef.setSelection(
          index,
          blot.domNode.innerText.length,
          Quill.sources.SILENT,
        );

        this.rangeToInsertText = this.getRange();
        this.openMenu(event, false, blot.domNode);
      }
    } catch (error) {
      console.warn('Error finding blot:', error);
    }
  }

  /**
   * Get current range from Quill editor
   */
  private getRange(): { index: number; length: number } {
    if (!this.quillEditorRef) {
      return { index: 0, length: 0 };
    }

    let range = this.quillEditorRef.getSelection();
    if (!range) {
      range = {
        index: this.quillEditorRef.getLength() - 1,
        length: 0,
      };
    }
    return range;
  }

  /**
   * Reset last typed character
   */
  private resetLastTypedChar(): void {
    this.lastTypedChar = '';
  }

  /**
   * Insert auto-suggest node when trigger character is typed
   */
  private insertAutoSuggestNode(): void {
    if (!this.quillEditorRef) return;

    // Get the current cursor position AFTER the trigger character was typed
    let range = this.getRange();
    const rangeIndex = range.index;
    const rangeLength = range.length;

    // If selected text, delete text and calc new range post deletion
    if (rangeLength) {
      this.deleteText(rangeIndex, rangeLength);
      range = this.getRange();
    }

    // Store the position where trigger character was typed
    // The trigger character is at position (range.index - 1) because cursor moved after typing
    // But we need to verify this is actually the trigger character
    const triggerIndex = range.index - 1;
    const textAtPosition = this.quillEditorRef.getText(triggerIndex, 1);

    if (textAtPosition === this.autoCompleteChar) {
      this.rangeToInsertText = {
        index: triggerIndex, // Position of the trigger character
        length: 1, // Include the trigger character in the length
      };
    } else {
      // Fallback: search backwards for the trigger character
      let foundIndex = -1;
      for (let i = range.index - 1; i >= Math.max(0, range.index - 10); i--) {
        if (this.quillEditorRef.getText(i, 1) === this.autoCompleteChar) {
          foundIndex = i;
          break;
        }
      }

      if (foundIndex >= 0) {
        this.rangeToInsertText = {
          index: foundIndex,
          length: 1,
        };
      } else {
        // Last resort: use current position
        this.rangeToInsertText = {
          index: range.index,
          length: 0,
        };
      }
    }

    // Reset the character tracking for new input
    this.charAfterSpecialSymbol = '';

    // Open the menu at current position
    this.openMenuAtCurrentPosition();
  }

  /**
   * Open menu at current cursor position
   */
  private openMenuAtCurrentPosition(): void {
    if (!this.quillEditorRef) return;

    const selection = this.quillEditorRef.getSelection();
    if (selection) {
      const bounds = this.quillEditorRef.getBounds(selection.index);
      const editorContainer = this.quillEditorRef.container;

      if (bounds && editorContainer) {
        const rect = editorContainer.getBoundingClientRect();

        // Configure auto-suggest mode
        this.isAutoSuggestMode = true;
        this.resetLastTypedChar();
        this.charAfterSpecialSymbol = '';
        this.initAutoSuggestModeEventHandlers();

        // Create position for popup
        const position: PopupPosition = {
          top: rect.top + bounds.top,
          left: rect.left + bounds.left,
          height: bounds.height,
        };

        // Open popup menu
        this.popupService.openMenu(
          this.suggestions,
          position,
          this.popupCallbacks,
        );
      }
    }
  }

  /**
   * Delete text from editor
   */
  private deleteText(from: number, length: number): void {
    if (this.quillEditorRef) {
      this.quillEditorRef.deleteText(from, length, Quill.sources.SILENT);
    }
  }

  /**
   * Open the suggestions menu (legacy method - now uses popup service)
   */
  private openMenu(
    _event: any,
    autoSuggestMode: boolean,
    caretNodeDom?: HTMLElement,
  ): void {
    if (autoSuggestMode) {
      this.isAutoSuggestMode = true;
      this.resetLastTypedChar();
      this.charAfterSpecialSymbol = '';
      this.initAutoSuggestModeEventHandlers();
    }

    if (caretNodeDom) {
      const rect = caretNodeDom.getBoundingClientRect();
      const position: PopupPosition = {
        top: rect.top,
        left: rect.left,
        height: rect.height,
      };

      this.popupService.openMenu(
        this.suggestions,
        position,
        this.popupCallbacks,
      );
    }
  }

  /**
   * Initialize auto-suggest mode event handlers
   */
  private initAutoSuggestModeEventHandlers(): void {
    if (this.quillEditorRef) {
      this.quillEditorRef.root.addEventListener(
        'keydown',
        this.getTypedChar,
        false,
      );
    }
  }

  /**
   * Get typed character for auto-suggest mode
   */
  private getTypedChar = (event: KeyboardEvent): void => {
    const inputChar = event.key.toLowerCase();

    // Check for underscore key
    if (event.shiftKey && event.key === '_') {
      this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + '_';
    }

    // Handle backspace/delete keys specifically
    if (event.key === 'Backspace') {
      if (this.charAfterSpecialSymbol.length > 0) {
        // We have characters after trigger character to delete
        this.charAfterSpecialSymbol = this.charAfterSpecialSymbol.slice(0, -1);
      } else {
        // charAfterSpecialSymbol is empty, so user is trying to delete the trigger character itself
        this.postPopUpCloseCallback();
        return;
      }
    }
    // Check for input was a letter, number, hyphen, underscore or space
    else if (/[a-z0-9-_ ]/.test(inputChar)) {
      // Regular character input
      if (event.shiftKey && event.key === this.autoCompleteChar) {
        this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + event.key;
      } else {
        this.charAfterSpecialSymbol = this.charAfterSpecialSymbol + inputChar;
      }
    } else {
      // Other keys - ignore
      return;
    }

    // Filter suggestions based on typed characters
    // When charAfterSpecialSymbol is empty, show all suggestions (user is back to just trigger character)
    if (this.charAfterSpecialSymbol === '') {
      this.suggestions = [...this.originalSuggestions];
    } else {
      this.suggestions = this.originalSuggestions.filter((listItem) => {
        return (
          listItem.display
            .toLowerCase()
            .indexOf(this.charAfterSpecialSymbol.toLowerCase()) >= 0
        );
      });
    }

    this.lastTypedChar = this.charAfterSpecialSymbol;

    // Update popup menu with filtered suggestions
    if (this.isAutoSuggestMode) {
      // Always keep popup open in auto-suggest mode, even with 0 suggestions
      const selection = this.quillEditorRef?.getSelection();
      if (selection && this.quillEditorRef) {
        const bounds = this.quillEditorRef.getBounds(selection.index);
        const editorContainer = this.quillEditorRef.container;

        if (bounds && editorContainer) {
          const rect = editorContainer.getBoundingClientRect();

          const position: PopupPosition = {
            top: rect.top + bounds.top,
            left: rect.left + bounds.left,
            height: bounds.height,
          };

          // Always open/update the menu when in auto-suggest mode
          this.popupService.openMenu(
            this.suggestions,
            position,
            this.popupCallbacks,
          );
        }
      }
    }
  };

  /**
   * Delete text block fields when backspace is pressed
   */
  private deleteTextBlockFields(): void {
    this.resetLastTypedChar();

    if (!this.quillEditorRef) return;

    const selectedRange = this.quillEditorRef.getSelection();

    if (selectedRange) {
      // Check if we're at a text block position
      const format = this.quillEditorRef.getFormat(selectedRange.index);

      // Only handle deletion if we're actually at a text block
      if (format && format['editortextblock']) {
        try {
          const blot = this.quillEditorRef.getLeaf(selectedRange.index);
          if (blot[0] && blot[0].domNode && blot[0].domNode.parentElement) {
            blot[0].domNode.parentElement.classList.add('read-write');
            const foundBlot = (Parchment as any).find(blot[0].domNode);
            if (foundBlot && foundBlot.domNode) {
              const textLength = foundBlot.domNode.innerText?.length || 0;
              this.quillEditorRef.setSelection(
                selectedRange.index - textLength,
                textLength,
                Quill.sources.SILENT,
              );
            }
          }
        } catch {
          // Silently handle errors
        }
      }
    }
  }

  /**
   * Callback to run when popup is closed
   */
  private postPopUpCloseCallback(): void {
    // Close the popup explicitly
    this.popupService.closeMenu();

    this.charAfterSpecialSymbol = '';
    this.suggestions = [...this.originalSuggestions];
    this.isAutoSuggestMode = false;

    // Remove event listeners
    if (this.quillEditorRef) {
      this.quillEditorRef.root.removeEventListener(
        'keydown',
        this.getTypedChar,
        false,
      );
    }
  }

  /**
   * Insert a new text block (normal mode)
   */
  private insertNewTextBlock(id: string, label: string): void {
    // Get current cursor position for insertion
    let insertionRange = this.rangeToInsertText;

    if (!insertionRange) {
      insertionRange = this.getRange();
    }

    // Calculate what needs to be deleted
    let deleteLength = 0;
    if (this.isAutoSuggestMode) {
      // Delete the trigger character + any typed characters
      // Always delete at least 1 character (the trigger) plus any additional typed characters
      deleteLength = 1 + this.charAfterSpecialSymbol.length;
    }

    // Perform the insertion with proper range handling
    this.insertTextBlockDirectly(id, label, insertionRange, deleteLength);
  }

  /**
   * Insert text block directly with proper range handling
   */
  private insertTextBlockDirectly(
    id: string,
    label: string,
    range: { index: number; length: number },
    deleteLength: number,
  ): void {
    if (!this.quillEditorRef) {
      return;
    }
    const newLabel = `<--${label}-->`;

    try {
      // Apply text block insertion with proper indexing
      const delta: any[] = [];

      // Retain content before the deletion point
      if (range.index > 0) {
        delta.push({ retain: range.index });
      }

      // Delete the trigger character and any typed characters
      if (deleteLength > 0) {
        delta.push({ delete: deleteLength });
      }

      // Insert the text block blot
      delta.push({
        insert: {
          editortextblock: {
            label: newLabel,
            id: id,
            length: newLabel.length,
          },
        },
      });
      delta.push({ insert: ' ' });

      // Apply the delta as a single atomic operation
      this.quillEditorRef.updateContents(delta, Quill.sources.USER);

      // Set cursor position after the text block and space
      // Embed blots have length 1 in Quill, plus 1 for the space = 2 total
      const newCursorPosition = range.index + 2;

      // Set selection immediately since editor is properly initialized
      this.quillEditorRef.setSelection(newCursorPosition, 0, Quill.sources.SILENT);

    } catch {
      // Silently handle errors
    }
  }

  /**
   * Replace an existing text block with a new one
   */
  private replaceExistingTextBlock(newId: string, newLabel: string): void {
    if (!this.quillEditorRef || !this.editingTextNode) return;

    try {
      // Use Quill's scroll to find the blot - this is the correct Quill 2.0 approach
      const scroll = this.quillEditorRef.scroll;
      const blot = scroll.find(this.editingTextNode);

      if (blot) {
        const index = this.quillEditorRef.getIndex(blot);
        const length = 1; // EmbedBlots have length 1

        // Delete the existing text block blot
        this.quillEditorRef.deleteText(index, length, 'user');

        // Insert the new text block at the same position
        const delta = [
          { retain: index },
          {
            insert: {
              editortextblock: {
                label: `<--${newLabel}-->`,
                id: newId,
                length: newLabel.length,
              },
            },
          },
        ];

        this.quillEditorRef.updateContents(delta, 'user');

        // Set cursor position after the new text block
        this.quillEditorRef.setSelection(index + 1, 0);
      }
    } catch {
      // Silently handle errors
    }
  }
}
