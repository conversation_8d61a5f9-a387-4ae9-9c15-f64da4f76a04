import { Injectable } from '@angular/core';
import { CdpDashboardApiService } from './api/cdp-dashboard-api.service';
import { BehaviorSubject, filter, of, switchMap } from 'rxjs';
import { CdpUserService } from './cdp-user.service';

@Injectable()
export class CdpDashboardService {
  /**
   * @private
   */
  private dashboardIdSubject: BehaviorSubject<number> = new BehaviorSubject(-1);
  /**
   * this will send the request to server to fetch the db def
   */
  public dashboardDef$ = this.dashboardIdSubject.asObservable().pipe(
    filter((id) => id > 0),
    switchMap(
      (id) => {
        const tenantDetails = this.userService.getTenantDetails();
        if (tenantDetails) {
          return this.dashboardApiService.getDashboardDef(tenantDetails.id, id);
        }
        return of(undefined);
      }
    )
  )

  /**
   * Load this dashboard
   * @param dashboardId
   */
  public init(dashboardId: number): void {
    this.dashboardIdSubject.next(dashboardId);
  }
  /**
   * Def constructor
   */
  constructor(private dashboardApiService: CdpDashboardApiService,
              private userService: CdpUserService) {
  }
}
