# Quill Tags - Plug and Play Implementation

A modular, reusable Tags functionality for Quill editors that can be easily integrated into any project.

## 🎯 Features

- **🔌 Plug and Play**: Easy integration with any Quill editor
- **🎨 Styled**: Pre-styled with design system colors
- **⌨️ Keyboard Navigation**: Full keyboard support (Arrow keys, Enter, Escape)
- **🔄 Text Replacement**: Replaces selected text or inserts at cursor
- **📝 Customizable**: Configurable suggestions, format, and callbacks
- **🎭 Theme Support**: Works with both bubble and snow themes
- **🏷️ Default Suggestions**: 21 pre-built meaningful tag suggestions

## 📦 Files Structure

```
extentions/
├── quill-tags.service.ts      # Core service with all functionality
├── quill-tags.helper.ts       # Helper utilities for easy integration
├── quill-tags.styles.scss     # Standalone styles
└── README-TAGS.md            # This documentation
```

## 🚀 Quick Start

### Option 1: Using Helper (Recommended)

```typescript
import { addDefaultTagsToQuill } from './extentions/quill-tags.helper';

export class MyEditorComponent {
  ngOnInit() {
    // Setup Tags with default suggestions
    const tags = addDefaultTagsToQuill((tag) => {
      console.log('Tag selected:', tag);
    });
    
    this.quillConfig = this.setupQuillConfig(tags);
  }
  
  private setupQuillConfig(tags: any) {
    const container = [
      ['bold', 'italic', 'underline'],
      [{ color: [...] }, 'link']
    ];
    
    // Add Tags to toolbar
    tags.addToToolbar(container);
    
    return {
      toolbar: {
        container,
        handlers: tags.getHandlers()
      }
    };
  }
  
  onEditorCreated(editor: Quill) {
    // Initialize Tags with editor
    const tags = addDefaultTagsToQuill((tag) => {
      this.onTagSelected(tag);
    });
    tags.onEditorCreated(editor);
  }
}
```

### Option 2: Using Service with DI

```typescript
import { QuillTagsService, createQuillTagsHelper } from './extentions';

export class MyEditorComponent {
  constructor(private tagsService: QuillTagsService) {}
  
  ngOnInit() {
    const helper = createQuillTagsHelper(this.tagsService);
    const tags = helper.setupWithDefaults((tag) => {
      this.onTagSelected(tag);
    });
    
    // Rest same as Option 1...
  }
}
```

### Option 3: Custom Suggestions

```typescript
import { addTagsToQuill } from './extentions/quill-tags.helper';

export class MyEditorComponent {
  ngOnInit() {
    const customSuggestions = [
      { id: 'user-name', display: 'User Name' },
      { id: 'user-email', display: 'User Email' },
      { id: 'company', display: 'Company Name' }
    ];
    
    const tags = addTagsToQuill({
      suggestions: customSuggestions,
      onTagSelected: (tag) => this.onTagSelected(tag),
      insertFormat: 'textblock', // or 'text'
      addSpace: true
    });
    
    // Setup same as above...
  }
}
```

## 🎨 Styling

### Import Styles

```scss
// In your component SCSS
@import './extentions/quill-tags.styles.scss';
```

### Custom Theme Colors

```scss
// Override default colors
.ql-bubble .ql-picker.ql-tags {
  .ql-picker-label:hover {
    color: #your-color !important;
  }
  
  .ql-picker-item:hover {
    background-color: #your-bg-color !important;
  }
}
```

### Alternative Themes

```scss
// Apply blue theme
.my-editor {
  @extend .ql-tags-theme-blue;
}

// Apply green theme  
.my-editor {
  @extend .ql-tags-theme-green;
}
```

## 📋 API Reference

### TagSuggestion Interface

```typescript
interface TagSuggestion {
  id: string;        // Unique identifier
  display: string;   // Text shown in picker and inserted
}
```

### TagsConfig Interface

```typescript
interface TagsConfig {
  suggestions: TagSuggestion[];           // Array of tag suggestions
  onTagSelected?: (tag: TagSuggestion) => void;  // Callback when tag selected
  insertFormat?: 'textblock' | 'text';    // Format for insertion
  addSpace?: boolean;                     // Add space after tag
}
```

### QuillTagsService Methods

```typescript
class QuillTagsService {
  initialize(config: TagsConfig): void
  setEditor(editor: Quill): void
  addTagsToToolbar(container: QuillToolbarConfig): QuillToolbarConfig
  getToolbarHandlers(): { [key: string]: (value: string) => void }
  updateSuggestions(suggestions: TagSuggestion[]): void
  insertTag(suggestion: TagSuggestion): void
  static getDefaultSuggestions(): TagSuggestion[]
}
```

### Helper Functions

```typescript
// Quick setup with defaults
addDefaultTagsToQuill(onTagSelected?: (tag: TagSuggestion) => void)

// Custom setup
addTagsToQuill(config: {
  suggestions: TagSuggestion[];
  onTagSelected?: (tag: TagSuggestion) => void;
  insertFormat?: 'textblock' | 'text';
  addSpace?: boolean;
})

// With dependency injection
createQuillTagsHelper(tagsService: QuillTagsService)
```

## 🏷️ Default Suggestions

The service includes 21 pre-built suggestions organized by category:

### 👤 User Information
- User Name, User Email, User Phone, User Company, User Title

### 📅 Date & Time  
- Current Date, Current Time, Due Date, Created Date

### 📄 Document
- Document Title, Document ID, Document Version

### ⚙️ System
- System Name, Environment, Application URL

### 🏢 Custom
- Project Name, Department, Location, Reference Number, Status

## 🔧 Advanced Usage

### Dynamic Suggestions

```typescript
// Update suggestions at runtime
this.tagsService.updateSuggestions(newSuggestions);
```

### Programmatic Insertion

```typescript
// Insert tag programmatically
const tag = { id: 'user-name', display: 'User Name' };
this.tagsService.insertTag(tag);
```

### Multiple Editors

```typescript
// Each editor can have its own Tags instance
const editor1Tags = addDefaultTagsToQuill(callback1);
const editor2Tags = addDefaultTagsToQuill(callback2);

// Setup each editor separately
editor1Tags.onEditorCreated(editor1);
editor2Tags.onEditorCreated(editor2);
```

## 🎯 Benefits

### ✅ **Modular Design**
- Separate service, helper, and styles
- No tight coupling to specific components
- Easy to test and maintain

### ✅ **Plug and Play**
- Add to any Quill editor with 3 lines of code
- No complex setup or configuration required
- Works with existing toolbar configurations

### ✅ **Reusable**
- Use across multiple projects
- Consistent behavior and styling
- Easy to customize and extend

### ✅ **Production Ready**
- Handles edge cases (text replacement, cursor positioning)
- Proper error handling and fallbacks
- TypeScript support with full type safety

## 🧪 Testing

```typescript
// Test tag insertion
const mockTag = { id: 'test', display: 'Test Tag' };
component.tagsService.insertTag(mockTag);

// Verify callback was called
expect(component.onTagSelected).toHaveBeenCalledWith(mockTag);
```

## 🔄 Migration

### From Inline Implementation

```typescript
// Before (inline in component)
getQuillConfig() {
  // Custom tags logic mixed with component
}

// After (using Tags service)
ngOnInit() {
  const tags = addDefaultTagsToQuill(this.onTagSelected.bind(this));
  this.quillConfig = this.setupQuillConfig(tags);
}
```

This modular approach provides clean separation of concerns and makes the Tags functionality truly plug-and-play!
