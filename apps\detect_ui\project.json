{"name": "detect_ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "hcldetect", "sourceRoot": "apps/detect_ui/src", "tags": ["app:detect-ui"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/detect_ui", "index": "apps/detect_ui/src/index.html", "main": "apps/detect_ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/detect_ui/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/detect_ui/public"}], "styles": ["apps/detect_ui/src/styles.scss", "public/assets/styles/unica/angular/unica.scss"], "scripts": [], "customWebpackConfig": {"path": "apps/detect_ui/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/detect_ui/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 7701, "publicHost": "http://localhost:7701"}, "configurations": {"production": {"buildTarget": "detect_ui:build:production"}, "development": {"buildTarget": "detect_ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "detect_ui:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/detect_ui/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "detect_ui:build", "port": 7701, "watch": false}, "configurations": {"development": {"buildTarget": "detect_ui:build:development"}, "production": {"buildTarget": "detect_ui:build:production"}}}}}