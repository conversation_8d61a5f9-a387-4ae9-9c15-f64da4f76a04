import { Injectable } from '@angular/core';
import { ApplicationToken, createHash, UnicaLogin, UserConfig } from '@hcl/unica-common';
import { HttpClient } from '@angular/common/http';
import {
  CdpLoginResponse,
  CdpTenant,
  CdpTenantDetails,
  CdpUserDetails
} from '../../model/cdp-login';
import { map, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CdpUserApiService {
  /**
   * Default constructor
   * @param http
   */
  constructor(private http: HttpClient) {
  }
  /**
   * Send request to the server for login
   * @param loginDetails
   */
  public login(loginDetails: UnicaLogin) {
    const formData = `username=${encodeURIComponent(loginDetails.userName)}&password=${createHash(loginDetails.password ?? '')}&grant_type=password`;
    return this.http.post<CdpLoginResponse>('/coreapi/oauth2/token', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization':'Basic bnVsbDpudWxs'
      }
    }).pipe(
      map((t) => {
        return {
          accessToken: t.access_token,
          userName: loginDetails.userName,
          refreshToken: t.refresh_token,
          expiration: new Date() /// TODO : need to cghange this
        } as ApplicationToken
      })
    );
  }
  /**
   * Send request to the server for getting user details
   * @param loginDetails
   */
  public getUserTenant() {
    return this.http.get<CdpTenant>('/dashbackend/app/login').pipe(
      switchMap((t) => {
        // now lets get the details of the tenant from the server
        return this.getUserTenantDetails(t.campaignId);
      })
    );
  }
  /**
   * Send request to the server for getting tenant details
   * @param loginDetails
   */
  public getUserTenantDetails(tenantId: number) {
    return this.http.get<{data: CdpTenantDetails}>('/coreapi/-/v1/advertisers/'+ tenantId)
      .pipe(
        map((d) => {
          return d.data;
        })
      );
  }
  /**
   * Get the details of the current user
   */
  getUserDetails() {
    return this.http.get<{data: CdpUserDetails[]}>('/coreapi/-/v1/users').pipe(
      map((userDetails) => {
        if (userDetails && userDetails.data && userDetails.data.length > 0) {
          return {
            firstName: userDetails.data[0].firstName,
            lastName: userDetails.data[0].firstName,
            username: userDetails.data[0].email,
            locale: userDetails.data[0].language,
            partitionId: 0,
            partitionName: ''
          } as UserConfig
        } else {
          throw new Error('Invalid Credentials');
        }
      })
    );
  }
}
