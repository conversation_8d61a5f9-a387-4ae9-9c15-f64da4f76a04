# Passing Suggestions to Unica Text Editor - Usage Guide

This guide shows how to pass dummy suggestions from a parent component down to the `unica-text-editor` component through the component hierarchy.

## 🎯 Data Flow

```
Parent Component → Text Block Component → Unica Text Editor → Tags Service
```

## 📋 Implementation Steps

### 1. Parent Component (App Level)

```typescript
// app.component.ts
export class AppComponent {
  // Define your dummy suggestions
  dummySuggestions = [
    { id: 'user-name', display: 'User Name' },
    { id: 'user-email', display: 'User Email' },
    { id: 'current-date', display: 'Current Date' },
    { id: 'company-name', display: 'Company Name' }
  ];
  
  onTagSelected(tag: any) {
    console.log('Tag selected:', tag);
  }
}
```

```html
<!-- app.component.html -->
<app-text-block
  [content]="content"
  [hashtagSuggestions]="dummySuggestions"
  (tagSelected)="onTagSelected($event)"
></app-text-block>
```

### 2. Text Block Component (Middle Layer)

```typescript
// text-block.component.ts
export class TextBlockComponent {
  @Input() hashtagSuggestions: any[] = [];
  @Output() tagSelected = new EventEmitter<any>();
  
  onTagSelected(tag: any) {
    this.tagSelected.emit(tag);
  }
}
```

```html
<!-- text-block.component.html -->
<unica-text-editor
  [content]="content"
  [hashtagSuggestions]="hashtagSuggestions"
  (hashtagSelected)="onTagSelected($event)"
></unica-text-editor>
```

### 3. Unica Text Editor (Final Component)

The `unica-text-editor` component now automatically:
- ✅ Receives suggestions via `@Input() hashtagSuggestions`
- ✅ Updates Tags service when suggestions change
- ✅ Passes suggestions to both toolbar and directive
- ✅ Emits `hashtagSelected` event when tag is selected

## 🚀 Quick Examples

### Example 1: Simple Direct Usage

```typescript
export class MyComponent {
  suggestions = [
    { id: 'name', display: 'Full Name' },
    { id: 'email', display: 'Email Address' },
    { id: 'date', display: 'Current Date' }
  ];
}
```

```html
<unica-text-editor
  [hashtagSuggestions]="suggestions"
  (hashtagSelected)="onTagSelected($event)"
></unica-text-editor>
```

### Example 2: Dynamic Suggestions

```typescript
export class MyComponent {
  currentSuggestions = this.getDefaultSuggestions();
  
  switchToEmailSuggestions() {
    this.currentSuggestions = [
      { id: 'recipient', display: 'Recipient Name' },
      { id: 'sender', display: 'Sender Name' },
      { id: 'subject', display: 'Email Subject' }
    ];
  }
  
  switchToInvoiceSuggestions() {
    this.currentSuggestions = [
      { id: 'invoice-num', display: 'Invoice Number' },
      { id: 'amount', display: 'Total Amount' },
      { id: 'due-date', display: 'Due Date' }
    ];
  }
}
```

```html
<div>
  <button (click)="switchToEmailSuggestions()">Email Tags</button>
  <button (click)="switchToInvoiceSuggestions()">Invoice Tags</button>
</div>

<unica-text-editor
  [hashtagSuggestions]="currentSuggestions"
  (hashtagSelected)="onTagSelected($event)"
></unica-text-editor>
```

### Example 3: Multiple Editors with Different Suggestions

```typescript
export class MyComponent {
  emailSuggestions = [
    { id: 'recipient', display: 'Recipient Name' },
    { id: 'subject', display: 'Email Subject' }
  ];
  
  reportSuggestions = [
    { id: 'report-title', display: 'Report Title' },
    { id: 'report-date', display: 'Report Date' }
  ];
}
```

```html
<div class="editor-grid">
  <div>
    <h3>Email Editor</h3>
    <unica-text-editor
      [hashtagSuggestions]="emailSuggestions"
      (hashtagSelected)="onEmailTagSelected($event)"
    ></unica-text-editor>
  </div>
  
  <div>
    <h3>Report Editor</h3>
    <unica-text-editor
      [hashtagSuggestions]="reportSuggestions"
      (hashtagSelected)="onReportTagSelected($event)"
    ></unica-text-editor>
  </div>
</div>
```

## 📝 Suggestion Format

Each suggestion must follow this interface:

```typescript
interface TagSuggestion {
  id: string;        // Unique identifier
  display: string;   // Text shown in picker and inserted
}
```

### Example Suggestions by Category

#### 👤 User Information
```typescript
[
  { id: 'user-name', display: 'User Name' },
  { id: 'user-email', display: 'User Email' },
  { id: 'user-phone', display: 'User Phone' },
  { id: 'user-company', display: 'User Company' },
  { id: 'user-title', display: 'User Title' }
]
```

#### 📅 Date & Time
```typescript
[
  { id: 'current-date', display: 'Current Date' },
  { id: 'current-time', display: 'Current Time' },
  { id: 'due-date', display: 'Due Date' },
  { id: 'created-date', display: 'Created Date' }
]
```

#### 📄 Document
```typescript
[
  { id: 'doc-title', display: 'Document Title' },
  { id: 'doc-id', display: 'Document ID' },
  { id: 'doc-version', display: 'Document Version' },
  { id: 'author-name', display: 'Author Name' }
]
```

#### 💼 Business
```typescript
[
  { id: 'invoice-number', display: 'Invoice Number' },
  { id: 'customer-name', display: 'Customer Name' },
  { id: 'total-amount', display: 'Total Amount' },
  { id: 'project-name', display: 'Project Name' }
]
```

## 🔄 Dynamic Updates

The component automatically updates when suggestions change:

```typescript
export class MyComponent {
  suggestions = [/* initial suggestions */];
  
  ngOnInit() {
    // Suggestions will be passed to editor
  }
  
  updateSuggestions() {
    // Change suggestions - editor will automatically update
    this.suggestions = [/* new suggestions */];
  }
}
```

## 🎯 Event Handling

```typescript
export class MyComponent {
  onTagSelected(tag: { id: string; display: string }) {
    console.log('Selected tag:', tag);
    
    // Handle tag selection
    switch(tag.id) {
      case 'user-name':
        // Handle user name tag
        break;
      case 'current-date':
        // Handle date tag
        break;
      default:
        // Handle other tags
    }
  }
}
```

## 🧪 Testing

```typescript
// Test component with dummy suggestions
const dummySuggestions = [
  { id: 'test-1', display: 'Test Tag 1' },
  { id: 'test-2', display: 'Test Tag 2' }
];

// Pass to component
component.hashtagSuggestions = dummySuggestions;

// Verify tags appear in dropdown
// Verify tag insertion works
// Verify events are emitted
```

## 📁 File Structure

```
src/
├── app/
│   ├── app.component.ts           # Parent component with suggestions
│   └── app.component.html
├── text-block/
│   ├── text-block.component.ts    # Middle layer component
│   └── text-block.component.html
└── unica-text-editor/
    ├── unica-text-editor.component.ts  # Final component
    └── extentions/
        └── quill-tags.service.ts        # Tags service
```

This setup provides a clean, modular way to pass suggestions through the component hierarchy while maintaining separation of concerns and reusability.
