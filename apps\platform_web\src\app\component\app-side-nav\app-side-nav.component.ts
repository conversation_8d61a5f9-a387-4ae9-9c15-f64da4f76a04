import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { PlatformNavigationService } from '../../service/platform-navigation.service';
import { ApplicationService, UnicaSpinnerService } from '@hcl/angular/unica-angular-common';
import { map } from 'rxjs';
import { UnicaSideNavElementItemConfig, UnicaSideNavSectionConfig } from '@hcl/unica-common';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';


@Component({
  selector: 'pf-side-nav',
  templateUrl: './app-side-nav.component.html',
  styleUrl: './app-side-nav.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppSideNavComponent {
  // this will tell if teh menu is loading
  protected isLoading$ = this.platformNavigationService.loadingMenu$;
  /**
   * The nav elements
   * @protected
   */
  protected navSections$ = this.platformNavigationService.navigationUpdated$.pipe(
    map((arr) => {
      // as per the UX we have 2 sections in the side navigation
      // 1. With home & fav
      // 2. the product navigation
      const menuSections:UnicaSideNavSectionConfig[] = [];
      menuSections.push({
        elements: [
          {
            id: 'home',
            icon: 'home',
            label: this.translate.instant('UNICA_COMMON.LABELS.HOME'),
            key: 'home',
            order: 1
          },
          {
            id: 'fav',
            icon: 'unica_favorite',
            label: this.translate.instant('UNICA_COMMON.LABELS.FAVOURITE'),
            children: [],
            order: 2
          }
        ]
      });
      // TODO: This is additional systems that we have not yet registered in Platform,
      // we will add them as well, this code needs to be removed & is intended for demo only
      arr.push({
        order: 0,
        children:[{
            id: 'home',
            label: 'Home',
            icon: 'home',
            path: 'home',
            order:0
          }, {
            id: 'event',
            label: 'Event',
            icon: 'widgets',
            path: 'event',
            order:0
          }
        ],
        icon: 'unica_detect',
        label: 'Detect',
        id: 'detect',
        path:'detect_ui'
      });
      arr.push({
        order: 0,
        children:[],
        icon: 'unica_cdp',
        label: 'CDP',
        id: 'cdp',
        path:'cdp'
      });
      ///////////////////////////////////////////////////
      menuSections.push({
        elements: arr
      });
      return menuSections;
    })
  );
  /**
   * @param applicationService
   */
  constructor(private applicationService: ApplicationService,
              private spinnerService: UnicaSpinnerService,
              private router: Router,
              private translate: TranslateService,
              protected platformNavigationService: PlatformNavigationService) {
  }
  /**
   * This function will navigate the user to the specific page
   * @param $event
   */
  protected navigate(event: UnicaSideNavElementItemConfig[]) {
    // TODO: This is a sample code & production this needs to be changed
    // the last element is the root node that is clicked
    const clickedNode = event[event.length -1];
    if (event[0].id === 'detect') {
      // navigate to the remote detect application
      //this.router.navigate(event.map((e) => e.path));
      this.router.navigate(['platform', ...event.map((e) => e.path)]);
    } else if (event[0].id === 'cdp') {
      // navigate to the remote cdp application
      //this.router.navigate(event.map((e) => e.path));
      this.router.navigate(['platform', ...event.map((e) => e.path)]);
    }
  }
}
