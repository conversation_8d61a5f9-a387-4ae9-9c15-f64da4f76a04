import { Component } from '@angular/core';
import { UnicaSideNavElementItemConfig, UnicaSideNavSectionConfig } from '@hcl/unica-common';
import { Route, Router } from '@angular/router';
import { DetectAppRoutes } from './detect.routes';

@Component({
  selector: 'hcldetect-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})

export class AppComponent {
  /**
   * The navigation elements for this application
   */
  navSections: UnicaSideNavSectionConfig[] = [];

  /**
   * Def constructor
   */
  constructor(private route: Router) {
    this.createNavRouting();
  }
  /**
   * This function will use the application routing and
   * create the nav elements
   * @private
   */
  private createNavRouting(): void {
    const navRoutes = this.routesToNav(DetectAppRoutes);
    this.navSections = [
      {
        elements: [
          {
            id: 'home',
            label: 'Home',
            icon: 'home',
            path: '/home',
            order:0
          },
          ...navRoutes
        ]
      }
    ];
  }
  /**
   * Recursively convert routes[] to nav
   * @param routes
   * @private
   */
  private routesToNav(routes: Route[], parentPath?: string): UnicaSideNavElementItemConfig[] {
    const arr: UnicaSideNavElementItemConfig[] = [];
    routes.forEach((r) => {
      // ignore default routes & home, rest will be in one
      if (r.path != '' && r.data && r.path != 'home') {
        const def: UnicaSideNavElementItemConfig = {
          id: r.data['id'],
          label: r.data['label'],
          icon: r.data['icon'],
          path: (parentPath ? parentPath + '/' : '') + r.path,
          children: [],
          order: 0
        }
        if (r.children) {
          def.children = this.routesToNav(r.children, r.path);
        }
        arr.push(def);
      }
    });
    return arr;
  }
  /**
   * Navigate
   * @param s
   */
  navigate(nav: UnicaSideNavElementItemConfig[]) {
    // get the last 1 and navigate
    const leafNode = nav[nav.length - 1];
    if (leafNode.path) {
      this.route.navigate([leafNode.path]);
    }
  }
}

