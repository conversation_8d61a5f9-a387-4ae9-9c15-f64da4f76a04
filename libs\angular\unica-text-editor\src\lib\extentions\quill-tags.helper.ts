import { QuillToolbarConfig } from 'ngx-quill';
import { QuillTagsService, TagSuggestion, TagsConfig } from './quill-tags.service';
import Quill from 'quill';

/**
 * Helper utility for easy integration of Tags functionality into any Quill editor
 * This provides a simple plug-and-play interface
 */
export class QuillTagsHelper {
  private tagsService: QuillTagsService;

  constructor(tagsService: QuillTagsService) {
    this.tagsService = tagsService;
  }

  /**
   * Easy setup method - call this to add Tags to your Quill editor
   * @param config - Tags configuration
   * @returns Object with toolbar config and handlers
   */
  setup(config: TagsConfig): {
    addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
    getHandlers: () => { [key: string]: (value: string) => void };
    onEditorCreated: (editor: Quill) => void;
  } {
    // Initialize the service
    this.tagsService.initialize(config);

    return {
      addToToolbar: (container: QuillToolbarConfig) => {
        return this.tagsService.addTagsToToolbar(container);
      },
      getHandlers: () => {
        return this.tagsService.getToolbarHandlers();
      },
      onEditorCreated: (editor: Quill) => {
        this.tagsService.setEditor(editor);
      }
    };
  }

  /**
   * Quick setup with default suggestions
   */
  setupWithDefaults(onTagSelected?: (tag: TagSuggestion) => void): {
    addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
    getHandlers: () => { [key: string]: (value: string) => void };
    onEditorCreated: (editor: Quill) => void;
  } {
    const config: TagsConfig = {
      suggestions: QuillTagsService.getDefaultSuggestions(),
      onTagSelected,
      insertFormat: 'textblock',
      addSpace: true
    };

    return this.setup(config);
  }
}

/**
 * Factory function to create a QuillTagsHelper instance
 */
export function createQuillTagsHelper(tagsService: QuillTagsService): QuillTagsHelper {
  return new QuillTagsHelper(tagsService);
}

/**
 * Standalone function for quick integration without dependency injection
 * Use this when you want to quickly add Tags to a Quill editor
 */
export function addTagsToQuill(config: {
  suggestions: TagSuggestion[];
  onTagSelected?: (tag: TagSuggestion) => void;
  insertFormat?: 'textblock' | 'text';
  addSpace?: boolean;
}): {
  addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
  getHandlers: () => { [key: string]: (value: string) => void };
  onEditorCreated: (editor: Quill) => void;
} {
  const tagsService = new QuillTagsService();
  const helper = new QuillTagsHelper(tagsService);
  
  return helper.setup({
    suggestions: config.suggestions,
    onTagSelected: config.onTagSelected,
    insertFormat: config.insertFormat || 'textblock',
    addSpace: config.addSpace !== false
  });
}

/**
 * Quick setup function with default suggestions
 */
export function addDefaultTagsToQuill(onTagSelected?: (tag: TagSuggestion) => void): {
  addToToolbar: (container: QuillToolbarConfig) => QuillToolbarConfig;
  getHandlers: () => { [key: string]: (value: string) => void };
  onEditorCreated: (editor: Quill) => void;
} {
  return addTagsToQuill({
    suggestions: QuillTagsService.getDefaultSuggestions(),
    onTagSelected
  });
}

/**
 * Example usage patterns for documentation
 */
export const USAGE_EXAMPLES = {
  // Example 1: Using with dependency injection
  withDI: `
    constructor(private tagsService: QuillTagsService) {}
    
    ngOnInit() {
      const helper = createQuillTagsHelper(this.tagsService);
      const tags = helper.setupWithDefaults((tag) => {
        console.log('Tag selected:', tag);
      });
      
      // In getQuillConfig():
      const container = [['bold', 'italic']];
      tags.addToToolbar(container);
      
      return {
        toolbar: {
          container,
          handlers: tags.getHandlers()
        }
      };
      
      // In onEditorCreated():
      tags.onEditorCreated(editor);
    }
  `,

  // Example 2: Standalone usage
  standalone: `
    ngOnInit() {
      const tags = addDefaultTagsToQuill((tag) => {
        this.hashtagSelected.emit(tag);
      });
      
      // In getQuillConfig():
      const container = [['bold', 'italic']];
      tags.addToToolbar(container);
      
      return {
        toolbar: {
          container,
          handlers: tags.getHandlers()
        }
      };
      
      // In onEditorCreated():
      tags.onEditorCreated(editor);
    }
  `,

  // Example 3: Custom suggestions
  custom: `
    ngOnInit() {
      const customSuggestions = [
        { id: 'name', display: 'Full Name' },
        { id: 'email', display: 'Email Address' }
      ];
      
      const tags = addTagsToQuill({
        suggestions: customSuggestions,
        onTagSelected: (tag) => this.onTagSelected(tag),
        insertFormat: 'text', // or 'textblock'
        addSpace: true
      });
      
      // Rest same as above...
    }
  `
};
