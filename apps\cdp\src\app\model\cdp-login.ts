/**
 * The response to the user login
 */
export interface CdpLoginResponse {
  expires_in: string;
  access_token: string;
  isFirstTime: number;
  isTFAEnabled: number;
  passwordExpired: number;
  refresh_token: string;
  token_type: string;
  uid: number;
}
/**
 * Response to get the user details
 */
export interface CdpUserDetailsResponse {
  m_partition_id: number;
  m_partition_name: string;
  m_user_email: string;
  m_user_first_name: string;
  m_user_id: number
  m_user_last_name: string
  m_user_locale: string
  m_user_name: string;
}
/**
 *
 */
export interface CdpTenant {
  campaignId: number;
}

/**
 * Teh default user config for a tenant
 */
export interface CdpTenantUserConfig {
  isCustomerOneViewEnabled: boolean;
  isSourceConnectionEnabled: boolean;
  isDestinationEnabled: boolean;
  isSegmentsAvailable: boolean;
  isChannelsAvailable: boolean;
  isJourneyBuilderEnabled: boolean;
  isOfflineDataSourceEnabled: boolean;
  isProfileUploadEnabled: boolean;
  isURLReportingEnabled: boolean;
  isFunnelReportsEnabled: boolean;
  isPathMetricsReportsEnabled: boolean;
  isCohortReportsEnabled: boolean;
}

/**
 * The config pf navigations
 */
export interface CdpNavigationConfiguration {
  dashboard?: {id: number};
  [key: string]: object | {id: number} | undefined;
}
/**
 * The tenant config
 */
export interface CdpTenantConfig {
  // if this value is enabled then we will pick the user leve config,
  // if this is false we will use the userLeveLConfig in CdpTenantDetails
  isUserLevelCheckEnabled?: boolean;
  featureEnablingConfig: CdpTenantUserConfig;
  // the configuration specific to navigation
  navConfig?: CdpNavigationConfiguration
}
/**
 *
 */
export interface CdpTenantDetails {
  id: number;
  name: string;
  timezone: string;
  timezoneOffset: string;
  currency: string;
  userLevelConfig:CdpTenantUserConfig;
  config: CdpTenantConfig;
}
/**
 *
 */
export interface CdpUserDetails {
  id: number,
  email: string,
  firstName: string,
  lastName: string,
  timeZone: string,
  language: string,
  type: string
}
