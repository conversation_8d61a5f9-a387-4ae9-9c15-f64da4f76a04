
/**
 * This is the list of custom icons that we use within unica
 * @private
 */
export const cdpIconList: {key: string, url: string}[] = [
  { key: 'unica_dashboard', url: 'assets/icons/cdp/dashboard.svg'},
  { key: 'unica_cov', url: 'assets/icons/cdp/customer-one-view.svg'},
  { key: 'unica_data_pipeline', url: 'assets/icons/cdp/data-pipeline.svg'},
  { key: 'unica_sources', url: 'assets/icons/cdp/sources.svg'},
  { key: 'unica_destinations', url: 'assets/icons/cdp/destinations.svg'},
  { key: 'unica_event_directory', url: 'assets/icons/cdp/calendar.svg'},
  { key: 'unica_profile_management', url: 'assets/icons/cdp/calendar.svg'},
  { key: 'unica_offline_data_sources', url: 'assets/icons/cdp/destinations.svg'},
  { key: 'unica_customer_properties', url: 'assets/icons/cdp/occurrence.svg'},
  { key: 'unica_profile_upload', url: 'assets/icons/cdp/occurrence.svg'},
  { key: 'unica_cdp_campaign', url: 'assets/icons/cdp/campaigns.svg'},
  { key: 'unica_channels', url: 'assets/icons/cdp/channels.svg'},
  { key: 'unica_segments', url: 'assets/icons/cdp/segments.svg'},
  { key: 'unica_journey', url: 'assets/icons/cdp/calendar.svg'},
  { key: 'unica_funnel', url: 'assets/icons/cdp/funnel.svg'},
  { key: 'unica_path', url: 'assets/icons/cdp/path.svg'},
  { key: 'unica_cohort', url: 'assets/icons/cdp/cohort.svg'},
  { key: 'unica_events', url: 'assets/icons/cdp/calendar.svg'},
  { key: 'unica_occurrence', url: 'assets/icons/cdp/occurrence.svg'}
]
