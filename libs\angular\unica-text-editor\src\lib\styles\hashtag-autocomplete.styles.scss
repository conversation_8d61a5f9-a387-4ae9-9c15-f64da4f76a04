.hashtag-suggestions-dropdown {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  min-width: 150px;
}

.hashtag-suggestion-item {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hashtag-suggestion-item:hover {
  background-color: #f1f1f1;
}

/* New improved hashtag autocomplete styles */
.suggestionListContainer {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  min-width: 200px;
}

.suggestionList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestionList .listItem {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.suggestionList .listItem:hover,
.suggestionList .listItem.highlight {
  background-color: #f1f1f1;
}

.suggestionList .listItem:last-child {
  border-bottom: none;
}

.suggestionList .listItem span {
  font-size: 14px;
  color: #333;
}

/* Autosuggest node styling */
.autosuggest {
  background-color: transparent;
  border: none;
  outline: none;
  display: inline;
  position: relative;
}

/* Hashtag/droppable element styling */
.droppable {
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 3px;
  padding: 2px 4px;
  margin: 0 2px;
  display: inline-block;
  cursor: pointer;
  user-select: none;
}

.droppable:hover {
  background-color: #bbdefb;
}

.droppable.read-write {
  background-color: #fff3e0;
  border-color: #ff9800;
}
