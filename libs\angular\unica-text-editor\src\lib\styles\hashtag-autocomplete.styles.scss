/**
 * Hashtag Autocomplete Directive Styles
 * Styles for the text autocomplete functionality
 */

.text-autocomplete-popup {
  position: absolute;
  background: white;
  border: 1px solid #d3d3e5;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  min-width: 200px;
  overflow-y: auto;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  // Smooth scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
  
  .suggestion-item {
    padding: 10px 14px;
    cursor: pointer;
    font-size: 14px;
    color: #1d1d23;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    
    &:hover,
    &.selected {
      background-color: #e0eeef;
      color: #006075;
      transform: translateX(2px);
    }
    
    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }
    
    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }
    
    // Add icon before text
    &::before {
      content: '#';
      margin-right: 8px;
      color: #006075;
      font-weight: bold;
      opacity: 0.7;
    }
    
    // Highlight matching text
    .highlight {
      background-color: #ffeb3b;
      color: #333;
      padding: 1px 2px;
      border-radius: 2px;
      font-weight: 600;
    }
  }
  
  // Empty state
  .no-suggestions {
    padding: 16px 14px;
    text-align: center;
    color: #666;
    font-style: italic;
    font-size: 13px;
  }
  
  // Loading state
  .loading {
    padding: 16px 14px;
    text-align: center;
    color: #666;
    font-size: 13px;
    
    &::before {
      content: '⏳';
      margin-right: 8px;
    }
  }
}

// Animation for popup appearance
.text-autocomplete-popup {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark theme support
.dark-theme {
  .text-autocomplete-popup {
    background: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
    
    .suggestion-item {
      color: #e0e0e0;
      border-bottom-color: #444;
      
      &:hover,
      &.selected {
        background-color: #404040;
        color: #4fc3f7;
      }
      
      &::before {
        color: #4fc3f7;
      }
    }
    
    .no-suggestions,
    .loading {
      color: #999;
    }
    
    &::-webkit-scrollbar-track {
      background: #333;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #555;
      
      &:hover {
        background: #666;
      }
    }
  }
}

// High contrast theme support
.high-contrast-theme {
  .text-autocomplete-popup {
    border: 2px solid #000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    
    .suggestion-item {
      border-bottom-color: #000;
      
      &:hover,
      &.selected {
        background-color: #000;
        color: #fff;
      }
    }
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .text-autocomplete-popup {
    max-height: 150px;
    min-width: 180px;
    font-size: 16px; // Prevent zoom on iOS
    
    .suggestion-item {
      padding: 12px 14px;
      font-size: 16px;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .text-autocomplete-popup {
    animation: none;
  }
  
  .suggestion-item {
    transition: none;
    
    &:hover,
    &.selected {
      transform: none;
    }
  }
}
