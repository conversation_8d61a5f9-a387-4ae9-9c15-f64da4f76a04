.hashtag-suggestions-dropdown {
  position: absolute;
  background-color: var(--unica-bg-overlay-pane, #FFFFFF);
  border: var(--unica-border-default, 1px) solid var(--unica-outline-card, #D3D3E5);
  border-radius: var(--unica-radius-max, 8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  min-width: 150px;
  font-family: HCLTechRoobert, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.hashtag-suggestion-item {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--unica-text-primary, #1D1D23);
  transition: background-color 0.2s ease;
}

.hashtag-suggestion-item:hover {
  background-color: var(--unica-bg-card-hover, #F5F5FA);
  color: var(--unica-hover-primary, #006075);
}

/* Improved hashtag autocomplete styles*/
.suggestionListContainer {
  position: absolute;
  background-color: var(--unica-bg-overlay-pane, #FFFFFF);
  border: var(--unica-border-default, 1px) solid var(--unica-outline-card, #D3D3E5);
  border-radius: var(--unica-radius-max, 8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  min-width: 200px;
  font-family: HCLTechRoobert, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.suggestionList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestionList .listItem {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: var(--unica-border-default, 1px) solid var(--unica-outline-card, #D3D3E5);
  transition: background-color 0.2s ease, color 0.2s ease;
  color: var(--unica-text-primary, #1D1D23);
}

.suggestionList .listItem:hover,
.suggestionList .listItem.highlight {
  background-color: var(--unica-bg-card-hover, #F5F5FA);
  color: var(--unica-hover-primary, #006075);
}

.suggestionList .listItem:last-child {
  border-bottom: none;
}

.suggestionList .listItem span {
  font-size: 14px;
  color: inherit;
}

/* Autosuggest node styling */
.autosuggest {
  background-color: transparent;
  border: none;
  outline: none;
  display: inline;
  position: relative;
}

/* Hashtag/droppable element styling*/
.droppable {
  background-color: var(--unica-primary-10, rgba(3, 141, 153, 0.1));
  border-radius: var(--unica-radius-default, 4px);
  margin: 0 2px;
  display: inline;
  cursor: pointer;
  user-select: none;
  color: var(--unica-primary, #038D99);
  font-weight: 500;
  transition: all 0.2s ease;
}

.droppable:hover {
  background-color: var(--unica-primary-20, rgba(3, 141, 153, 0.2));
  border-color: var(--unica-hover-primary, #006075);
  color: var(--unica-hover-primary, #006075);
}

.droppable.read-write {
  background-color: var(--unica-status-warning, #FF9914);
  border-color: var(--unica-status-warning, #FF9914);
  color: var(--unica-text-button, #F9F9F9);
}
