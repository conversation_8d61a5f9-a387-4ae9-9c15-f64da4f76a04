import { Injectable } from '@angular/core';

export interface SuggestionItem {
  id: string;
  display: string;
}

export interface PopupPosition {
  top: number;
  left: number;
  height: number;
}

export interface PopupCallbacks {
  onItemSelected: (item: SuggestionItem) => void;
  onMenuClosed: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class HashtagPopupService {
  // Popup menu elements
  private suggestionPopupMenu: HTMLElement | null = null;
  private suggestionList: HTMLElement | null = null;

  // State management
  private isMenuOpened = false;
  private callbacks: PopupCallbacks | null = null;

  // Navigation
  private traversedItemCount = -1;
  private currentTraversedElement: HTMLElement | null = null;

  // Current suggestions
  private currentSuggestions: SuggestionItem[] = [];

  constructor() {
    this.createPopupElements();
    this.setupGlobalEventListeners();
  }

  /**
   * Initialize the popup service
   */
  public initialize(): void {
    if (!this.suggestionPopupMenu) {
      this.createPopupElements();
    }
  }

  /**
   * Open the popup menu with suggestions
   */
  public openMenu(
    suggestions: SuggestionItem[],
    position: PopupPosition,
    callbacks: PopupCallbacks
  ): void {
    this.currentSuggestions = suggestions;
    this.callbacks = callbacks;
    this.traversedItemCount = -1;
    this.isMenuOpened = true;

    this.createMenuItems();
    this.positionMenu(position);
    this.showMenu();

    // Setup event listeners
    this.setupMenuEventListeners();
  }

  /**
   * Close the popup menu
   */
  public closeMenu(): void {
    if (!this.isMenuOpened) return;

    // Store callback reference before resetting state
    const onMenuClosed = this.callbacks?.onMenuClosed;

    this.hideMenu();
    this.cleanupEventListeners();
    this.resetState();

    // Call the callback after state is reset
    if (onMenuClosed) {
      onMenuClosed();
    }
  }

  /**
   * Close the popup menu without triggering onMenuClosed callback
   */
  private closeMenuSilently(): void {
    if (!this.isMenuOpened) return;

    this.hideMenu();
    this.cleanupEventListeners();
    this.resetState();

    // Don't call onMenuClosed callback
  }

  /**
   * Check if menu is currently open
   */
  public isOpen(): boolean {
    return this.isMenuOpened;
  }

  /**
   * Navigate to previous item (arrow up)
   */
  public navigatePrevious(): void {
    if (!this.isMenuOpened) return;

    this.traversedItemCount--;
    if (this.traversedItemCount < -1) {
      this.traversedItemCount = -1;
    }
    this.highlightItem();
  }

  /**
   * Navigate to next item (arrow down)
   */
  public navigateNext(): void {
    if (!this.isMenuOpened) return;

    this.traversedItemCount++;
    if (this.traversedItemCount === this.currentSuggestions.length) {
      this.traversedItemCount--;
      return;
    }
    this.highlightItem();
  }

  /**
   * Select the currently highlighted item
   */
  public selectCurrentItem(): void {
    if (!this.isMenuOpened || this.traversedItemCount < 0 || !this.callbacks) return;

    const selectedItem = this.currentSuggestions[this.traversedItemCount];
    if (selectedItem && this.callbacks.onItemSelected) {
      // Call the callback FIRST to preserve state
      this.callbacks.onItemSelected(selectedItem);

      // Then close menu silently (without triggering onMenuClosed callback)
      this.closeMenuSilently();
    }
  }

  /**
   * Scroll to and highlight a specific item
   */
  public scrollToAndSelectItem(targetItem: SuggestionItem): void {
    if (!this.suggestionList) return;

    const listItems = this.suggestionList.querySelectorAll('.listItem');
    let targetElement: HTMLElement | null = null;
    let targetIndex = -1;

    for (let i = 0; i < listItems.length; i++) {
      const item = listItems[i] as HTMLElement;
      const itemId = item.getAttribute('data-data');
      if (itemId === targetItem.id) {
        targetElement = item;
        targetIndex = i;
        break;
      }
    }

    if (targetElement) {
      // Remove previous highlights
      listItems.forEach(item => {
        (item as HTMLElement).style.backgroundColor = 'white';
      });

      // Highlight the target item
      targetElement.style.backgroundColor = '#e6f3ff';

      // Update traversed item count
      this.traversedItemCount = targetIndex;
      this.currentTraversedElement = targetElement;

      // Scroll into view
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }

  /**
   * Destroy the popup service and cleanup
   */
  public destroy(): void {
    this.closeMenu();
    this.removePopupElements();
    this.cleanupGlobalEventListeners();

    // Only nullify callbacks on destroy
    this.callbacks = null;
  }

  /**
   * Create popup DOM elements
   */
  private createPopupElements(): void {
    // Create main popup container
    this.suggestionPopupMenu = document.createElement('div');
    this.suggestionPopupMenu.className = 'suggestionListContainer';
    Object.assign(this.suggestionPopupMenu.style, {
      position: 'absolute',
      backgroundColor: 'white',
      border: '1px solid #ccc',
      borderRadius: '4px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: '10000',
      maxHeight: '200px',
      overflowY: 'auto',
      display: 'none',
      top: '-100000px',
      left: '-100000px',
    });

    // Create suggestion list
    this.suggestionList = document.createElement('ul');
    this.suggestionList.className = 'suggestionList';
    Object.assign(this.suggestionList.style, {
      listStyle: 'none',
      margin: '0',
      padding: '0',
    });

    this.suggestionPopupMenu.appendChild(this.suggestionList);
    document.body.appendChild(this.suggestionPopupMenu);
  }

  /**
   * Remove popup DOM elements
   */
  private removePopupElements(): void {
    if (this.suggestionPopupMenu && this.suggestionPopupMenu.parentNode) {
      this.suggestionPopupMenu.parentNode.removeChild(this.suggestionPopupMenu);
    }
    this.suggestionPopupMenu = null;
    this.suggestionList = null;
  }

  /**
   * Create menu items from suggestions
   */
  private createMenuItems(): void {
    if (!this.suggestionList) return;

    this.suggestionList.innerHTML = '';

    for (let i = 0; i < this.currentSuggestions.length; i++) {
      const liItem = document.createElement('li');
      const item = this.currentSuggestions[i];

      liItem.title = item.display;
      liItem.setAttribute('data-label', item.display);
      liItem.setAttribute('data-data', item.id);
      liItem.setAttribute('data-count', i.toString());
      liItem.className = 'listItem';

      // Style the list item
      Object.assign(liItem.style, {
        padding: '8px 16px',
        cursor: 'pointer',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        borderBottom: '1px solid #eee',
        backgroundColor: 'white',
        userSelect: 'none'
      });

      liItem.textContent = item.display;

      // Add click handler
      liItem.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        if (this.callbacks?.onItemSelected) {
          this.callbacks.onItemSelected(item);
          this.closeMenu();
        }
      });

      this.suggestionList.appendChild(liItem);
    }
  }

  /**
   * Position the menu at the specified location
   */
  private positionMenu(position: PopupPosition): void {
    if (!this.suggestionPopupMenu) return;

    this.suggestionPopupMenu.style.top = `${position.top + position.height}px`;
    this.suggestionPopupMenu.style.left = `${position.left - 65}px`;

    // Check if menu is in viewport and adjust if needed
    const isInViewport = this.isElementInViewport(this.suggestionPopupMenu);
    if (!isInViewport) {
      const rect = this.suggestionPopupMenu.getBoundingClientRect();
      this.suggestionPopupMenu.style.top = `${position.top - rect.height}px`;
    }
  }

  /**
   * Show the menu
   */
  private showMenu(): void {
    if (this.suggestionPopupMenu) {
      this.suggestionPopupMenu.style.display = 'block';
    }
  }

  /**
   * Hide the menu
   */
  private hideMenu(): void {
    if (this.suggestionPopupMenu) {
      this.suggestionPopupMenu.style.display = 'none';
      this.suggestionPopupMenu.style.top = '-100000px';
      this.suggestionPopupMenu.style.left = '-100000px';
    }
  }

  /**
   * Highlight the currently traversed item
   */
  private highlightItem(): void {
    this.unHighlightAllItems();

    if (this.traversedItemCount >= 0 && this.traversedItemCount < this.currentSuggestions.length) {
      const currentItem = document.querySelector(
        `li[data-count="${this.traversedItemCount}"]`
      ) as HTMLElement;

      if (currentItem) {
        currentItem.style.backgroundColor = '#e6f3ff';
        this.currentTraversedElement = currentItem;

        // Scroll into view if needed
        currentItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      }
    }
  }

  /**
   * Remove highlight from all items
   */
  private unHighlightAllItems(): void {
    for (let index = 0; index < this.currentSuggestions.length; index++) {
      const currentItem = document.querySelector(
        `li[data-count="${index}"]`
      ) as HTMLElement;
      if (currentItem) {
        currentItem.style.backgroundColor = 'white';
      }
    }
  }

  /**
   * Check if element is in viewport
   */
  private isElementInViewport(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  /**
   * Setup menu-specific event listeners
   */
  private setupMenuEventListeners(): void {
    if (this.suggestionList) {
      this.suggestionList.addEventListener('mousedown', this.handleMenuMouseDown, false);
    }
  }

  /**
   * Setup global event listeners
   */
  private setupGlobalEventListeners(): void {
    document.addEventListener('click', this.handleClickOutside, false);
  }

  /**
   * Cleanup menu-specific event listeners
   */
  private cleanupEventListeners(): void {
    if (this.suggestionList) {
      this.suggestionList.removeEventListener('mousedown', this.handleMenuMouseDown, false);
    }
  }

  /**
   * Cleanup global event listeners
   */
  private cleanupGlobalEventListeners(): void {
    document.removeEventListener('click', this.handleClickOutside, false);
  }

  /**
   * Handle mouse down on menu items
   */
  private handleMenuMouseDown = (event: MouseEvent): void => {
    event.preventDefault();
    event.stopPropagation();

    const target = event.target as HTMLElement;
    const itemData = target.getAttribute('data-data');
    const itemLabel = target.getAttribute('data-label');

    if (itemData && itemLabel && this.callbacks?.onItemSelected) {
      const selectedItem: SuggestionItem = {
        id: itemData,
        display: itemLabel
      };
      this.callbacks.onItemSelected(selectedItem);
      this.closeMenu();
    }
  };

  /**
   * Handle clicks outside the menu
   */
  private handleClickOutside = (event: MouseEvent): void => {
    if (
      this.isMenuOpened &&
      this.suggestionPopupMenu &&
      !this.suggestionPopupMenu.contains(event.target as Node)
    ) {
      this.closeMenu();
    }
  };

  /**
   * Reset internal state
   */
  private resetState(): void {
    this.isMenuOpened = false;
    this.traversedItemCount = -1;
    this.currentTraversedElement = null;
    this.currentSuggestions = [];
    // Don't nullify callbacks - they should persist for the directive lifetime
  }
}
