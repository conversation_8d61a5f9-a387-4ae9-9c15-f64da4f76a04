import { PlatformMenuItems, PlatformMenuResponse } from '../model/platform-menu';
import { UnicaSideNavElementItemConfig } from '@hcl/unica-common';
import { TranslateService } from '@ngx-translate/core';

/**
 * This adapter will convert PlatformMenuResponse to any other specif
 * format
 */
export class PlatformMenuResponseAdapter {
  // default key for the translation object
  private static readonly TRANSLATION_KEY = 'UNICA_COMMON.LABELS';
  /**
   * The buckets & the children
   * @private
   */
  private productBucketsMap: Map<string, string> = new Map<string, string>([
    ['audiencecentral', '*'], // in root
    ['segmentcentral', '*'], // in root
    ['offer', '*'], // in root
    ['campaignanalytics', 'Analytics'],
    ['deliveranalytics', 'Analytics'],
    ['interactanalytics', 'Analytics'],
    ['monitoringconsole', 'Analytics'],
    ['operationalanalytics', 'Analytics'],
    ['openinsights', 'Analytics'],
    ['auditreports', 'Analytics'],
    ['spss', 'Analytics'],
    ['allcampaigns', 'Campaign'],
    ['allsessions', 'Campaign'],
    ['monitoringconsole', 'Campaign'],
    ['optimize', 'Campaign'],
    ['delivermailings', 'Campaign'],
    ['deliverdocuments', 'Campaign'],
    ['newdeliverdocuments', 'Campaign'],
    ['interactivechannels', 'Interact'],
    ['built-inlearningdefinitions', 'Interact'],
    ['globaldefinitions', 'Interact'],
    ['interactrtpdashboard', 'RTP'],
    ['alljourneys', 'Journey'],
    ['entrysource', 'Journey'],
    ['journeyanalytics', 'Journey'],
    ['interactrtpaudiences', 'RTP'],
    ['interactrtpsettings', 'RTP'],
    ['interactrtpreports', 'RTP'],
    ['interactrtpwebdomains', 'RTP'],
    ['interactrtpcreatives', 'RTP']
  ]);
  /**
   * There are some keys that we need to ignore
   * These will not be part of menu
   * @private
   */
  private ignoreKeys = [
    'suite.uiNavigation.menuItem.separator',
    'Separator'
  ]
  /**
   * Default constructor
   * @param menuResp
   */
  constructor(private menuResp: PlatformMenuResponse,
              private translate: TranslateService) {
  }
  /**
   *
   */
  public toUnicaSideNavSection(): UnicaSideNavElementItemConfig[] {
    const arr: UnicaSideNavElementItemConfig[] = [];
    // let's iterate through the section & place it in respective buckets
    const prodMap: Map<string, UnicaSideNavElementItemConfig> = new Map<string, UnicaSideNavElementItemConfig>()
    const translationLabels = this.translate.instant(PlatformMenuResponseAdapter.TRANSLATION_KEY);
    Object.keys(this.menuResp).forEach((key) => {
      const details = this.menuResp[key];
      // iterate through the sections & map the menus
      details.forEach((item) => {
        // make sure this is not a ignored key
        if (this.ignoreKeys.findIndex((e) => e === item.key) < 0) {
          const bucketKey = this.productBucketsMap.get(item.key) ?? item.product;
          // if this is a global bucket then we have to add to the root
          if (bucketKey === '*') {
            arr.push(this.platformMenuItemToUnicaSideNavElementItemConfig(key, item))
          } else {
            // chk if this bucket is already created
            let bucket = prodMap.get(bucketKey);
            if (!bucket) {
              const label = translationLabels[bucketKey.toUpperCase()] ?? key;
              // we need to create the bucket
              bucket = this.platformMenuItemToUnicaSideNavElementItemConfig(bucketKey, {
                destinationURI: '',
                key: bucketKey,
                order: 0,
                popup: 'false',
                product: bucketKey,
                productOrder: 0,
                title: label,
                display: label
              });
              prodMap.set(bucketKey, bucket);
              arr.push(bucket);
            }
            // we have the bucket, now add a child to it
            bucket.children?.push(this.platformMenuItemToUnicaSideNavElementItemConfig(key, item));
          }
        }
      })
    });
    // sort the array
    //arr.sort((a, b) => <number>a.order - <number>b.order);
    // sort all the elements based on order
    arr.forEach((e) => {
      e.children = e.children?.sort((a, b) => <number>a.order - <number>b.order)
    })
    return arr;
  }
  /**
   * Convert a individual element to UnicaSideNavElementItemConfig
   * @constructor
   * @private
   */
  private platformMenuItemToUnicaSideNavElementItemConfig(key: string, e: PlatformMenuItems) : UnicaSideNavElementItemConfig {
    return {
      id: e.key ?? '',
      label: e.display ?? e.title,
      icon: this.getMenuIcon(e.key ?? ''),
      title: e.description ?? e.title,
      description: e.description ?? e.title,
      data: e,
      routerLink: 'content-block',
      signOnTargetUrl: e.destinationURI,
      productKey: e.product ? e.product : '',
      key: e.key,
      secondaryKey: key,
      order: e.order,
      popup: e.popup ? (e.popup.toLowerCase() === 'true' ? true : false) : false,
      children:[]
    };
  }
  /**
   * This will generate the default menu
   * @private
   */
  private getMenuIcon(key: string) : string {
    switch (key.toLowerCase()) {
      case 'campaign':
        return 'unica_campaign';
      case 'plan':
        return 'unica_plan';
      case 'interactdt':
      case 'interact':
        return 'unica_interact';
      case 'manager':
        return 'unica_interact';
      case 'journey':
        return 'unica_journey';
      case 'deliver':
        return 'unica_deliver';
      case 'marketingcentral':
        return 'unica_marketing_central';
      case 'cdp':
        return 'unica_cdp';
      case 'detect':
        return 'unica_detect';
      case 'analytics':
        return 'unica_analytics';
      case 'rtp':
        return 'unica_rtp';
      case 'offer':
        return 'unica_offer';
      case 'audiencecentral':
        return 'unica_audiencecentral';
      case 'segmentcentral':
        return 'unica_segmentcentral';
      default:
        return 'unica_';
    }
  }
}
