import Quill from 'quill';
import { Subject } from 'rxjs';

// Import EmbedBlot for standalone elements like hashtags
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const EmbedBlot = Quill.import('blots/embed') as any;

export interface TextNodeData {
  label: string;
  length: number;
  id?: string;
  messageBody?: string;
}

export interface TextNodeClickEvent {
  value: TextNodeData;
  action?: 'edit' | 'create';
  node?: HTMLElement;
}

export class EditorTextNode extends EmbedBlot {
  static blotName = 'editortextblock';
  static tagName = 'span';
  static className = 'droppable';

  static onTextNodeClick = new Subject<TextNodeClickEvent>();
  static TextNodeClick$ = EditorTextNode.onTextNodeClick.asObservable();

  static create(value: TextNodeData): HTMLElement {
    // Follow the exact pattern from Quill documentation
    const node = super.create() as HTMLElement;

    // Set content directly in the span
    node.innerHTML = value.label;

    // IMPORTANT: Add the droppable class for click detection
    node.classList.add('droppable');

    // Set attributes - only id and contenteditable
    node.setAttribute('contenteditable', 'false');
    if (value.id) {
      node.id = value.id;
    }

    // Store the value data as data attributes for event delegation
    node.setAttribute('data-text-node-id', value.id || '');
    node.setAttribute('data-text-node-label', value.label);
    node.setAttribute('data-text-node-length', value.length.toString());

    console.log('EditorTextNode.create - created node:', {
      node,
      classList: Array.from(node.classList),
      hasDroppable: node.classList.contains('droppable'),
      innerHTML: node.innerHTML,
      attributes: {
        id: node.id,
        contenteditable: node.getAttribute('contenteditable'),
        dataId: node.getAttribute('data-text-node-id'),
        dataLabel: node.getAttribute('data-text-node-label')
      }
    });

    // Don't attach individual click listeners - use event delegation instead
    // The directive will handle clicks via event delegation on the editor root

    return node;
  }

  // Override value method to return the data stored in the node
  static value(node: HTMLElement): TextNodeData {
    return {
      label: node.innerHTML || '',
      length: (node.innerHTML || '').length,
      id: node.id || undefined
    };
  }

  /**
   * Static method to trigger click events for event delegation
   * This handles clicks on both original and duplicated text nodes
   */
  static handleTextNodeClick(node: HTMLElement): void {
    // Extract data from the node (works for both original and duplicated nodes)
    let label = node.getAttribute('data-text-node-label') || '';
    let id = node.getAttribute('data-text-node-id') || node.id || undefined;

    // If no label from attributes, extract from DOM structure
    if (!label) {
      // Handle nested structure: get text from inner span if present
      const innerSpan = node.querySelector('span[contenteditable="false"]');
      if (innerSpan) {
        label = innerSpan.innerHTML || '';
      } else {
        label = node.innerHTML || '';
      }
    }

    // If we don't have a proper ID from attributes, try to extract it from the label
    if (!id && label) {
      // If label is in format <--Display Text-->, try to convert to kebab-case ID
      const cleanLabel = label.replace(/^&lt;--(.+)--&gt;$/, '$1').replace(/^<--(.+)-->$/, '$1');
      if (cleanLabel !== label) {
        // Convert display text to potential ID (e.g., "User Name" -> "user-name")
        id = cleanLabel.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      }
    }

    const value: TextNodeData = {
      label: label,
      length: parseInt(node.getAttribute('data-text-node-length') || label.length.toString(), 10),
      id: id
    };

    console.log('EditorTextNode.handleTextNodeClick:', {
      node,
      extractedValue: value,
      nodeAttributes: {
        id: node.id,
        dataId: node.getAttribute('data-text-node-id'),
        dataLabel: node.getAttribute('data-text-node-label'),
        innerHTML: node.innerHTML,
        innerSpanContent: node.querySelector('span[contenteditable="false"]')?.innerHTML
      }
    });

    // Emit click event
    EditorTextNode.onTextNodeClick.next({
      value,
      action: 'edit',
      node: node
    });
  }
}

// Note: Blot is registered in the component to avoid conflicts
